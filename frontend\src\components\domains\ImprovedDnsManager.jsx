"use client";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ody,
  <PERSON>ton,
  <PERSON>ert,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TabsBody,
  <PERSON>b,
  <PERSON>b<PERSON><PERSON>l,
  <PERSON><PERSON>,
} from "@material-tailwind/react";
import {
  Globe,
  Link,
  Mail,
  FileText,
  Server,
  Settings,
  AlertCircle,
  RefreshCw,
  Plus,
} from "lucide-react";
import DnsRecordTable from "./DnsRecordTable";
import DnsRecordForm from "./DnsRecordForm";
import domainMngService from "@/app/services/domainMngService";
import { toast } from "react-toastify";

export default function ImprovedDnsManager({ domain, onUpdate }) {
  const [dnsServiceActive, setDnsServiceActive] = useState(
    domain?.dnsActivated || false
  );
  const [allDnsRecords, setAllDnsRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activatingService, setActivatingService] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedRecordType, setSelectedRecordType] = useState("A");
  const [editingRecord, setEditingRecord] = useState(null);
  const [activeTab, setActiveTab] = useState("A");

  // DNS record types configuration
  const recordTypes = [
    { type: "A", label: "A", icon: Globe, description: "IPv4 addresses" },
    { type: "AAAA", label: "AAAA", icon: Globe, description: "IPv6 addresses" },
    {
      type: "CNAME",
      label: "CNAME",
      icon: Link,
      description: "Canonical names",
    },
    { type: "MX", label: "MX", icon: Mail, description: "Mail servers" },
    { type: "TXT", label: "TXT", icon: FileText, description: "Text records" },
    { type: "NS", label: "NS", icon: Server, description: "Name servers" },
    {
      type: "SRV",
      label: "SRV",
      icon: Settings,
      description: "Service records",
    },
  ];

  // Load DNS records
  const loadDnsRecords = async () => {
    try {
      setLoading(true);
      console.log(
        `🔍 Loading DNS records for domain: ${domain?.name} (ID: ${domain?.id})`
      );

      const response = await domainMngService.getDnsRecords(domain.id);
      console.log(`📋 DNS Records Response:`, response.data);

      if (response.data.success) {
        const records = response.data.records || [];
        setAllDnsRecords(records);
        setDnsServiceActive(true);
        console.log(`✅ Loaded ${records.length} DNS records`);

        // Log records by type for debugging
        recordTypes.forEach(({ type }) => {
          const typeRecords = records.filter((r) => r.type === type);
          console.log(
            `📊 ${type} Records (${typeRecords.length}):`,
            typeRecords
          );
        });
      } else {
        throw new Error(response.data.error || "Failed to load DNS records");
      }
    } catch (error) {
      console.error("❌ Error loading DNS records:", error);
      console.error("❌ Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      // If we can't get records, service might not be activated
      setDnsServiceActive(false);
      toast.error(
        "Failed to load DNS records. DNS service may not be activated."
      );
      setAllDnsRecords([]);
    } finally {
      setLoading(false);
    }
  };

  // Activate DNS service
  const activateDnsService = async () => {
    try {
      setActivatingService(true);
      console.log(`🚀 Activating DNS service for domain: ${domain?.name}`);
      console.log(`🔍 Domain object:`, domain);

      // The backend expects orderId, which should be the domain's order ID
      const orderId = domain.orderid || domain.domainOrderId || domain.id;
      console.log(`📋 Using order ID: ${orderId}`);

      const response = await domainMngService.activateDnsService(orderId);

      console.log(`📋 DNS Activation Response:`, response.data);

      if (response.data.success) {
        setDnsServiceActive(true);
        toast.success("DNS service activated successfully!");

        // Update the domain object with DNS activation status
        if (onUpdate) {
          onUpdate({
            dnsActivated: true,
            dnsActivatedAt: new Date().toISOString(),
            dnsZoneId: response.data.zoneId || null,
          });
        }

        await loadDnsRecords(); // Load records after activation
      } else {
        throw new Error(
          response.data.error || "Failed to activate DNS service"
        );
      }
    } catch (error) {
      console.error("❌ Error activating DNS service:", error);
      toast.error("Failed to activate DNS service");
    } finally {
      setActivatingService(false);
    }
  };

  // Add DNS record
  const handleAddRecord = async (recordData) => {
    try {
      console.log(`➕ Adding DNS record:`, recordData);

      const response = await domainMngService.addDnsRecord(
        domain.id,
        recordData
      );
      console.log(`📋 Add Record Response:`, response.data);

      if (response.data.success) {
        toast.success(`${recordData.type} record added successfully!`);
        setShowAddForm(false);
        setSelectedRecordType("A");
        await loadDnsRecords(); // Reload records
      } else {
        throw new Error(response.data.error || "Failed to add DNS record");
      }
    } catch (error) {
      console.error("❌ Error adding DNS record:", error);
      toast.error("Failed to add DNS record");
    }
  };

  // Edit DNS record
  const handleEditRecord = async (recordData) => {
    try {
      console.log(`✏️ Editing DNS record:`, recordData);

      const response = await domainMngService.updateDnsRecord(
        domain.id,
        editingRecord.id,
        recordData
      );

      console.log(`📋 Edit Record Response:`, response.data);

      if (response.data.success) {
        toast.success(`${recordData.type} record updated successfully!`);
        setEditingRecord(null);
        await loadDnsRecords(); // Reload records
      } else {
        throw new Error(response.data.error || "Failed to update DNS record");
      }
    } catch (error) {
      console.error("❌ Error updating DNS record:", error);
      toast.error("Failed to update DNS record");
    }
  };

  // Delete DNS record
  const handleDeleteRecord = async (recordId) => {
    try {
      console.log(`🗑️ Deleting DNS record ID: ${recordId}`);

      const response = await domainMngService.deleteDnsRecord(
        domain.id,
        recordId
      );
      console.log(`📋 Delete Record Response:`, response.data);

      if (response.data.success) {
        toast.success("DNS record deleted successfully!");
        await loadDnsRecords(); // Reload records
      } else {
        throw new Error(response.data.error || "Failed to delete DNS record");
      }
    } catch (error) {
      console.error("❌ Error deleting DNS record:", error);
      toast.error("Failed to delete DNS record");
    }
  };

  // Get records for specific type (filter out empty records)
  const getRecordsForType = (type) => {
    return allDnsRecords.filter(
      (record) =>
        record.type === type && record.content && record.content.trim() !== ""
    );
  };

  // Handle add button click
  const handleAddClick = (recordType) => {
    setSelectedRecordType(recordType);
    setShowAddForm(true);
  };

  // Update DNS service status when domain changes
  useEffect(() => {
    setDnsServiceActive(domain?.dnsActivated || false);
  }, [domain?.dnsActivated]);

  useEffect(() => {
    if (domain?.id) {
      loadDnsRecords();
    }
  }, [domain?.id]);

  if (!domain) {
    return (
      <Alert color="amber" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        Domain information is required to manage DNS records.
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="text-gray-900">
            DNS Management
          </Typography>
          <Typography className="text-gray-600 mt-1">
            Manage DNS records for {domain.name}
          </Typography>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outlined"
            onClick={loadDnsRecords}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          <Button
            onClick={() => handleAddClick(activeTab)}
            disabled={!dnsServiceActive}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Record
          </Button>
        </div>
      </div>

      {/* DNS Service Status */}
      {!dnsServiceActive && (
        <Alert color="amber" className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            <div>
              <Typography className="font-medium">
                DNS Service Not Active
              </Typography>
              <Typography className="text-sm">
                Activate DNS service to manage DNS records for this domain.
              </Typography>
            </div>
          </div>
          <Button
            size="sm"
            onClick={activateDnsService}
            disabled={activatingService}
            className="flex items-center gap-2"
          >
            {activatingService && <Spinner className="h-4 w-4" />}
            Activate DNS
          </Button>
        </Alert>
      )}

      {/* DNS Records Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs value={activeTab} onChange={setActiveTab}>
            <TabsHeader className="bg-gray-50 p-1 m-6 mb-0">
              {recordTypes.map(({ type, label }) => {
                const count = getRecordsForType(type).length;
                return (
                  <Tab
                    key={type}
                    value={type}
                    className="flex items-center gap-2"
                  >
                    <span>{label}</span>
                    {count > 0 && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                        {count}
                      </span>
                    )}
                  </Tab>
                );
              })}
            </TabsHeader>

            <TabsBody className="p-6">
              {recordTypes.map(({ type }) => (
                <TabPanel key={type} value={type} className="p-0">
                  <DnsRecordTable
                    records={getRecordsForType(type)}
                    recordType={type}
                    onEdit={setEditingRecord}
                    onDelete={handleDeleteRecord}
                    onAdd={handleAddClick}
                    domain={domain}
                    loading={loading}
                  />
                </TabPanel>
              ))}
            </TabsBody>
          </Tabs>
        </CardBody>
      </Card>

      {/* Add/Edit Record Form */}
      <DnsRecordForm
        isOpen={showAddForm || !!editingRecord}
        onClose={() => {
          setShowAddForm(false);
          setEditingRecord(null);
          setSelectedRecordType("A");
        }}
        onSubmit={editingRecord ? handleEditRecord : handleAddRecord}
        initialData={editingRecord}
        domain={domain}
        selectedType={selectedRecordType}
        onTypeChange={setSelectedRecordType}
      />
    </div>
  );
}
