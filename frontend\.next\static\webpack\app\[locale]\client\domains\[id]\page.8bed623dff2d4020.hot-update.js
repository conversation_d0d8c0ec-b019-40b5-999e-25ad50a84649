"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    var _domain_status, _domain_status1, _domain_status2, _domain_status3, _domain_status4, _domain_status5, _domain_status6, _domain_status7, _domain_status8, _domain_status9, _domain_privacyProtectionDetails, _domain_raaVerification, _domain_gdpr, _domain_status10, _domain_status11, _domain_contacts, _domain_contacts1, _domain_contacts2, _domain_contacts3;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Utility function to format Unix timestamps\n    const formatDate = (unixTimestamp)=>{\n        if (!unixTimestamp) return \"Not available\";\n        try {\n            const date = new Date(parseInt(unixTimestamp) * 1000);\n            return date.toLocaleDateString(\"en-US\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } catch (error) {\n            return \"Invalid date\";\n        }\n    };\n    // Utility function to format domain status\n    const formatStatus = (status)=>{\n        if (!status) return \"unknown\";\n        return status.toLowerCase().replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    // Utility function to safely get contact information\n    const getContactInfo = function(contact, field) {\n        let fallback = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"Not available\";\n        if (!contact || typeof contact !== \"object\") {\n            return fallback;\n        }\n        return contact[field] || fallback;\n    };\n    // Utility function to check if contact exists and has data\n    const hasContactData = (contact)=>{\n        return contact && typeof contact === \"object\" && (contact.name || contact.email);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Try to get detailed information from the reseller API\n                try {\n                    var _detailsRes_data, _apiDomain_privacyProtection, _apiDomain_privacyProtection1;\n                    console.log(\"\\uD83D\\uDD0D Fetching real domain details for:\", userDomain.name);\n                    // Get real domain details from reseller API\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details\n                    );\n                    console.log(\"✅ Real domain details from reseller API:\", detailsRes.data);\n                    const apiDomain = (_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain;\n                    // Debug privacy protection data\n                    console.log(\"\\uD83D\\uDD12 [PRIVACY DEBUG] API Privacy Data:\", {\n                        apiPrivacyProtection: apiDomain === null || apiDomain === void 0 ? void 0 : apiDomain.privacyProtection,\n                        userPrivacyProtection: userDomain.privacyProtection,\n                        apiPrivacyEnabled: apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection === void 0 ? void 0 : _apiDomain_privacyProtection.enabled,\n                        willUseApiData: (apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection1 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection1 === void 0 ? void 0 : _apiDomain_privacyProtection1.enabled) !== undefined\n                    });\n                    if (apiDomain) {\n                        var _apiDomain_privacyProtection2, _apiDomain_contactDetails, _apiDomain_contactDetails1, _apiDomain_contactDetails2, _apiDomain_contactDetails3;\n                        // Use real data from reseller API\n                        const combinedDomain = {\n                            id: userDomain.id,\n                            name: apiDomain.domainName || userDomain.name,\n                            status: apiDomain.status || userDomain.status,\n                            registrationDate: apiDomain.registrationDate || userDomain.registrationDate,\n                            expiryDate: apiDomain.expiryDate || userDomain.expiryDate,\n                            autoRenew: apiDomain.autoRenew || userDomain.autoRenew || false,\n                            registrar: \"ZTech Domains\",\n                            // Use real nameservers from API\n                            nameservers: apiDomain.nameservers && apiDomain.nameservers.length > 0 ? apiDomain.nameservers : userDomain.nameservers || [\n                                \"ns1.ztech\",\n                                \"ns2.ztech\",\n                                \"ns3.ztech\",\n                                \"ns4.ztech\"\n                            ],\n                            // Use real privacy protection data from reseller API (prioritize API data)\n                            privacyProtection: ((_apiDomain_privacyProtection2 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection2 === void 0 ? void 0 : _apiDomain_privacyProtection2.enabled) !== undefined ? apiDomain.privacyProtection.enabled : userDomain.privacyProtection || false,\n                            privacyProtectionDetails: apiDomain.privacyProtection,\n                            period: userDomain.period,\n                            price: userDomain.price,\n                            orderId: apiDomain.orderId || userDomain.orderId,\n                            orderStatus: apiDomain.orderStatus || userDomain.orderStatus,\n                            // Real contact details from API\n                            contacts: {\n                                registrant: ((_apiDomain_contactDetails = apiDomain.contactDetails) === null || _apiDomain_contactDetails === void 0 ? void 0 : _apiDomain_contactDetails.registrant) ? {\n                                    name: apiDomain.contactDetails.registrant.name,\n                                    email: apiDomain.contactDetails.registrant.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.registrant.telnocc, \" \").concat(apiDomain.contactDetails.registrant.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.registrant.address1, \", \").concat(apiDomain.contactDetails.registrant.city, \", \").concat(apiDomain.contactDetails.registrant.country, \" \").concat(apiDomain.contactDetails.registrant.zip),\n                                    company: apiDomain.contactDetails.registrant.company,\n                                    contactId: apiDomain.contactDetails.registrant.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                admin: ((_apiDomain_contactDetails1 = apiDomain.contactDetails) === null || _apiDomain_contactDetails1 === void 0 ? void 0 : _apiDomain_contactDetails1.admin) ? {\n                                    name: apiDomain.contactDetails.admin.name,\n                                    email: apiDomain.contactDetails.admin.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.admin.telnocc, \" \").concat(apiDomain.contactDetails.admin.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.admin.address1, \", \").concat(apiDomain.contactDetails.admin.city, \", \").concat(apiDomain.contactDetails.admin.country, \" \").concat(apiDomain.contactDetails.admin.zip),\n                                    company: apiDomain.contactDetails.admin.company,\n                                    contactId: apiDomain.contactDetails.admin.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                technical: ((_apiDomain_contactDetails2 = apiDomain.contactDetails) === null || _apiDomain_contactDetails2 === void 0 ? void 0 : _apiDomain_contactDetails2.tech) ? {\n                                    name: apiDomain.contactDetails.tech.name,\n                                    email: apiDomain.contactDetails.tech.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.tech.telnocc, \" \").concat(apiDomain.contactDetails.tech.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.tech.address1, \", \").concat(apiDomain.contactDetails.tech.city, \", \").concat(apiDomain.contactDetails.tech.country, \" \").concat(apiDomain.contactDetails.tech.zip),\n                                    company: apiDomain.contactDetails.tech.company,\n                                    contactId: apiDomain.contactDetails.tech.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                billing: ((_apiDomain_contactDetails3 = apiDomain.contactDetails) === null || _apiDomain_contactDetails3 === void 0 ? void 0 : _apiDomain_contactDetails3.billing) ? {\n                                    name: apiDomain.contactDetails.billing.name,\n                                    email: apiDomain.contactDetails.billing.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.billing.telnocc, \" \").concat(apiDomain.contactDetails.billing.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.billing.address1, \", \").concat(apiDomain.contactDetails.billing.city, \", \").concat(apiDomain.contactDetails.billing.country, \" \").concat(apiDomain.contactDetails.billing.zip),\n                                    company: apiDomain.contactDetails.billing.company,\n                                    contactId: apiDomain.contactDetails.billing.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                }\n                            },\n                            // Contact IDs for API operations\n                            contactIds: apiDomain.contacts,\n                            // Additional real data from API\n                            productCategory: apiDomain.productCategory,\n                            productKey: apiDomain.productKey,\n                            customerId: apiDomain.customerId,\n                            gdpr: apiDomain.gdpr,\n                            locks: apiDomain.locks,\n                            raaVerification: apiDomain.raaVerification,\n                            dnssec: apiDomain.dnssec,\n                            // Raw API response for debugging\n                            apiDetails: apiDomain,\n                            // Default DNS records (placeholder - would need separate API call)\n                            dnsRecords: [\n                                {\n                                    id: \"rec1\",\n                                    type: \"A\",\n                                    name: \"@\",\n                                    content: \"DNS records available via separate API\",\n                                    ttl: 14400\n                                }\n                            ]\n                        };\n                        setDomain(combinedDomain);\n                    } else {\n                        throw new Error(\"No domain data received from API\");\n                    }\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"ns1.ztech\",\n                            \"ns2.ztech\",\n                            \"ns3.ztech\",\n                            \"ns4.ztech\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            technical: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 14400\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] Toggling privacy protection for domain \".concat(domain.name, \":\"), {\n                from: domain.privacyProtection,\n                to: value,\n                orderId: domain.orderId\n            });\n            if (!domain.orderId) {\n                console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ No order ID available for domain\");\n                return;\n            }\n            // Call the modify privacy protection API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].modifyPrivacyProtection({\n                orderId: domain.orderId,\n                protectPrivacy: value,\n                reason: \"User \".concat(value ? \"enabled\" : \"disabled\", \" privacy protection via domain management panel\")\n            });\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] ✅ Privacy protection updated successfully:\", response.data);\n            // Update local state\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n            // Show success message\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Privacy protection \".concat(value ? \"enabled\" : \"disabled\", \" successfully\"));\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ Error toggling privacy protection:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to update privacy protection. Please try again.\");\n        }\n    };\n    const handlePurchasePrivacy = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDED2 [PRIVACY] Purchasing privacy protection for domain \".concat(domain.name, \":\"), {\n                orderId: domain.orderId\n            });\n            if (!domain.orderId) {\n                console.error(\"\\uD83D\\uDED2 [PRIVACY] ❌ No order ID available for domain\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Unable to purchase privacy protection. Order ID not found.\");\n                return;\n            }\n            // Call the purchase privacy protection API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].purchasePrivacyProtection({\n                orderId: domain.orderId,\n                invoiceOption: \"NoInvoice\",\n                discountAmount: 0\n            });\n            console.log(\"\\uD83D\\uDED2 [PRIVACY] ✅ Privacy protection purchased successfully:\", response.data);\n            // Update local state - privacy protection is now purchased and enabled\n            setDomain({\n                ...domain,\n                privacyProtection: true,\n                privacyProtectionDetails: {\n                    ...domain.privacyProtectionDetails,\n                    purchased: true,\n                    enabled: true\n                }\n            });\n            // Show success message\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Privacy protection purchased and enabled successfully!\");\n        } catch (error) {\n            console.error(\"\\uD83D\\uDED2 [PRIVACY] ❌ Error purchasing privacy protection:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to purchase privacy protection. Please try again.\");\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 391,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 406,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(((_domain_status = domain.status) === null || _domain_status === void 0 ? void 0 : _domain_status.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status1 = domain.status) === null || _domain_status1 === void 0 ? void 0 : _domain_status1.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status2 = domain.status) === null || _domain_status2 === void 0 ? void 0 : _domain_status2.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status3 = domain.status) === null || _domain_status3 === void 0 ? void 0 : _domain_status3.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(((_domain_status4 = domain.status) === null || _domain_status4 === void 0 ? void 0 : _domain_status4.toLowerCase()) || \"unknown\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: dt(\"manage_dns_records\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outlined\",\n                                                className: \"border-purple-600 text-purple-600 hover:bg-purple-50 flex items-center gap-2\",\n                                                onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns\")),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"manage_dns_records\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Order ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                domain.orderId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(((_domain_status5 = domain.status) === null || _domain_status5 === void 0 ? void 0 : _domain_status5.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status6 = domain.status) === null || _domain_status6 === void 0 ? void 0 : _domain_status6.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status7 = domain.status) === null || _domain_status7 === void 0 ? void 0 : _domain_status7.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status8 = domain.status) === null || _domain_status8 === void 0 ? void 0 : _domain_status8.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(((_domain_status9 = domain.status) === null || _domain_status9 === void 0 ? void 0 : _domain_status9.toLowerCase()) || \"unknown\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.registrationDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.expiryDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: \"Security & Protection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-end gap-2\",\n                                                                            children: ((_domain_privacyProtectionDetails = domain.privacyProtectionDetails) === null || _domain_privacyProtectionDetails === void 0 ? void 0 : _domain_privacyProtectionDetails.purchased) !== false ? // Privacy protection is purchased - show toggle\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        checked: domain.privacyProtection,\n                                                                                        onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                                        color: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 616,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 rounded \".concat(domain.privacyProtection ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                                        children: domain.privacyProtection ? \"Enabled\" : \"Disabled\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 623,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 615,\n                                                                                columnNumber: 29\n                                                                            }, this) : // Privacy protection not purchased - show purchase option\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col items-end gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded\",\n                                                                                        children: \"Not Purchased\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 633,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        color: \"blue\",\n                                                                                        variant: \"outlined\",\n                                                                                        className: \"text-xs px-3 py-1\",\n                                                                                        onClick: ()=>handlePurchasePrivacy(),\n                                                                                        children: \"Purchase Privacy Protection\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 636,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderStatus && Array.isArray(domain.orderStatus) && domain.orderStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Domain Locks\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 652,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.orderStatus.map((lock, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded\",\n                                                                                    children: lock\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 657,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.domainStatus && Array.isArray(domain.domainStatus) && domain.domainStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registry Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 667,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.domainStatus.map((status, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                                                    children: status\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 672,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_raaVerification = domain.raaVerification) === null || _domain_raaVerification === void 0 ? void 0 : _domain_raaVerification.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"RAA Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.raaVerification.status === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerification.status === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerification.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_gdpr = domain.gdpr) === null || _domain_gdpr === void 0 ? void 0 : _domain_gdpr.enabled) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"GDPR Protection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.gdpr.enabled === \"true\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                            children: domain.gdpr.enabled === \"true\" ? \"Enabled\" : \"Disabled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                (((_domain_status10 = domain.status) === null || _domain_status10 === void 0 ? void 0 : _domain_status10.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" || domain.registrationError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registration Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 715,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-100 text-red-800 rounded\",\n                                                                                    children: \"Registration Failed\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 719,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                domain.registrationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-50 text-red-700 rounded\",\n                                                                                    children: domain.registrationError\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 723,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 718,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers && Array.isArray(domain.nameservers) && domain.nameservers.length > 0 ? domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 747,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 743,\n                                                                        columnNumber: 27\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_domain_status11 = domain.status) === null || _domain_status11 === void 0 ? void 0 : _domain_status11.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" ? \"Nameservers not available - Registration failed\" : \"No nameservers configured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 754,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 764,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts = domain.contacts) === null || _domain_contacts === void 0 ? void 0 : _domain_contacts.registrant) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 795,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 799,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 803,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 806,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 809,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 819,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts1 = domain.contacts) === null || _domain_contacts1 === void 0 ? void 0 : _domain_contacts1.admin) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 832,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 836,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.admin, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.admin, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.admin, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.admin, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 850,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 856,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 825,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts2 = domain.contacts) === null || _domain_contacts2 === void 0 ? void 0 : _domain_contacts2.technical) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 869,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 873,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.technical, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 877,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.technical, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 880,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.technical, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 883,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.technical, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 887,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 893,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts3 = domain.contacts) === null || _domain_contacts3 === void 0 ? void 0 : _domain_contacts3.billing) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 908,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 912,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.billing, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.billing, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 919,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.billing, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 922,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.billing, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 926,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 932,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 955,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 423,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 422,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});