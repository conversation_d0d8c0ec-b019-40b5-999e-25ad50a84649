"use client";
import { useState } from "react";
import {
  Card,
  CardBody,
  <PERSON>po<PERSON>,
  Button,
  IconButton,
  <PERSON>lt<PERSON>,
  <PERSON>,
  Dialog,
  DialogHeader,
  DialogBody,
  DialogFooter,
} from "@material-tailwind/react";
import {
  Globe,
  Link,
  Mail,
  FileText,
  Server,
  Settings,
  Edit,
  Trash2,
  Copy,
  Plus,
  AlertTriangle,
} from "lucide-react";
import { TTL_OPTIONS } from "@/constants/dnsRecords";

export default function DnsRecordTable({
  records,
  recordType,
  onEdit,
  onDelete,
  onAdd,
  domain,
  loading = false,
}) {
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  // Get record type configuration
  const getRecordTypeConfig = (type) => {
    const configs = {
      A: {
        icon: Globe,
        label: "IPv4 Address",
        color: "blue",
        bgColor: "bg-blue-50",
        textColor: "text-blue-600",
        borderColor: "border-blue-200",
      },
      AAAA: {
        icon: Globe,
        label: "IPv6 Address",
        color: "purple",
        bgColor: "bg-purple-50",
        textColor: "text-purple-600",
        borderColor: "border-purple-200",
      },
      CNAME: {
        icon: Link,
        label: "Canonical Name",
        color: "green",
        bgColor: "bg-green-50",
        textColor: "text-green-600",
        borderColor: "border-green-200",
      },
      MX: {
        icon: Mail,
        label: "Mail Exchange",
        color: "orange",
        bgColor: "bg-orange-50",
        textColor: "text-orange-600",
        borderColor: "border-orange-200",
      },
      TXT: {
        icon: FileText,
        label: "Text Record",
        color: "gray",
        bgColor: "bg-gray-50",
        textColor: "text-gray-600",
        borderColor: "border-gray-200",
      },
      NS: {
        icon: Server,
        label: "Name Server",
        color: "indigo",
        bgColor: "bg-indigo-50",
        textColor: "text-indigo-600",
        borderColor: "border-indigo-200",
      },
      SRV: {
        icon: Settings,
        label: "Service Record",
        color: "pink",
        bgColor: "bg-pink-50",
        textColor: "text-pink-600",
        borderColor: "border-pink-200",
      },
    };
    return configs[type] || configs.A;
  };

  // Format TTL display
  const formatTTL = (ttl) => {
    const numericTTL = parseInt(ttl);
    const option = TTL_OPTIONS.find((opt) => opt.value === numericTTL);
    if (option) {
      return option.label;
    }
    
    // Format custom TTL values
    if (numericTTL >= 86400) {
      const days = Math.floor(numericTTL / 86400);
      const remainder = numericTTL % 86400;
      if (remainder === 0) {
        return `${days} day${days !== 1 ? 's' : ''}`;
      }
    } else if (numericTTL >= 3600) {
      const hours = Math.floor(numericTTL / 3600);
      const remainder = numericTTL % 3600;
      if (remainder === 0) {
        return `${hours} hour${hours !== 1 ? 's' : ''}`;
      }
    } else if (numericTTL >= 60) {
      const minutes = Math.floor(numericTTL / 60);
      const remainder = numericTTL % 60;
      if (remainder === 0) {
        return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
      }
    }
    
    return `${numericTTL}s`;
  };

  // Format record name
  const formatRecordName = (name) => {
    if (!name || name === "@") return domain?.name || "@";
    if (name.endsWith(".")) return name.slice(0, -1);
    return name;
  };

  // Copy to clipboard
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  // Handle delete confirmation
  const handleDeleteClick = (record) => {
    setDeleteConfirm(record);
  };

  const handleDeleteConfirm = () => {
    if (deleteConfirm && onDelete) {
      onDelete(deleteConfirm.id);
    }
    setDeleteConfirm(null);
  };

  const config = getRecordTypeConfig(recordType);
  const IconComponent = config.icon;

  return (
    <>
      <Card className="w-full">
        <CardBody className="p-0">
          {/* Header */}
          <div className={`${config.bgColor} ${config.borderColor} border-b px-6 py-4`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${config.bgColor} ${config.borderColor} border`}>
                  <IconComponent className={`h-5 w-5 ${config.textColor}`} />
                </div>
                <div>
                  <Typography variant="h6" className="text-gray-900">
                    {recordType} Records
                  </Typography>
                  <Typography variant="small" className="text-gray-600">
                    {config.label} • {records.length} record{records.length !== 1 ? 's' : ''}
                  </Typography>
                </div>
              </div>
              <Button
                size="sm"
                className={`flex items-center gap-2 ${config.textColor} bg-white border ${config.borderColor} hover:${config.bgColor}`}
                variant="outlined"
                onClick={() => onAdd && onAdd(recordType)}
              >
                <Plus className="h-4 w-4" />
                Add {recordType}
              </Button>
            </div>
          </div>

          {/* Table */}
          {loading ? (
            <div className="px-6 py-12 text-center">
              <Typography className="text-gray-500">Loading {recordType} records...</Typography>
            </div>
          ) : records.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <div className="flex flex-col items-center gap-3">
                <div className={`p-3 rounded-full ${config.bgColor}`}>
                  <IconComponent className={`h-6 w-6 ${config.textColor}`} />
                </div>
                <div>
                  <Typography variant="h6" className="text-gray-900 mb-1">
                    No {recordType} records found
                  </Typography>
                  <Typography className="text-gray-500 text-sm">
                    Create your first {recordType} record to get started
                  </Typography>
                </div>
                <Button
                  size="sm"
                  className={`flex items-center gap-2 ${config.textColor}`}
                  variant="outlined"
                  onClick={() => onAdd && onAdd(recordType)}
                >
                  <Plus className="h-4 w-4" />
                  Add {recordType} Record
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Table Header */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                  <div className="col-span-3">Name</div>
                  <div className="col-span-5">Value</div>
                  <div className="col-span-2">TTL</div>
                  <div className="col-span-2 text-right">Actions</div>
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-200">
                {records.map((record, index) => (
                  <div
                    key={record.id}
                    className="px-6 py-4 hover:bg-gray-50 transition-colors duration-150"
                  >
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Name Column */}
                      <div className="col-span-3">
                        <Typography className="font-medium text-gray-900 text-sm">
                          {formatRecordName(record.name)}
                        </Typography>
                      </div>

                      {/* Value Column */}
                      <div className="col-span-5">
                        <div className="flex items-center gap-2">
                          <Typography className="font-mono text-sm text-gray-700 flex-1 min-w-0 truncate">
                            {record.content}
                          </Typography>
                          <Tooltip content="Copy to clipboard">
                            <IconButton
                              variant="text"
                              size="sm"
                              onClick={() => copyToClipboard(record.content)}
                              className="p-1 text-gray-400 hover:text-gray-600"
                            >
                              <Copy className="h-3 w-3" />
                            </IconButton>
                          </Tooltip>
                        </div>
                        {record.priority && (
                          <Typography className="text-xs text-gray-500 mt-1">
                            Priority: {record.priority}
                          </Typography>
                        )}
                      </div>

                      {/* TTL Column */}
                      <div className="col-span-2">
                        <Chip
                          value={formatTTL(record.ttl)}
                          size="sm"
                          className="bg-gray-100 text-gray-700 text-xs"
                        />
                      </div>

                      {/* Actions Column */}
                      <div className="col-span-2 flex justify-end gap-1">
                        <Tooltip content="Edit record">
                          <IconButton
                            variant="text"
                            size="sm"
                            onClick={() => onEdit && onEdit(record)}
                            className="text-gray-400 hover:text-blue-600"
                          >
                            <Edit className="h-4 w-4" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip content="Delete record">
                          <IconButton
                            variant="text"
                            size="sm"
                            onClick={() => handleDeleteClick(record)}
                            className="text-gray-400 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </IconButton>
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </CardBody>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deleteConfirm} handler={() => setDeleteConfirm(null)} size="sm">
        <DialogHeader className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-red-500" />
          Confirm Delete
        </DialogHeader>
        <DialogBody>
          <Typography>
            Are you sure you want to delete this {deleteConfirm?.type} record?
          </Typography>
          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
            <Typography className="text-sm font-medium text-gray-900">
              {formatRecordName(deleteConfirm?.name)} → {deleteConfirm?.content}
            </Typography>
          </div>
        </DialogBody>
        <DialogFooter className="gap-2">
          <Button variant="outlined" onClick={() => setDeleteConfirm(null)}>
            Cancel
          </Button>
          <Button color="red" onClick={handleDeleteConfirm}>
            Delete Record
          </Button>
        </DialogFooter>
      </Dialog>
    </>
  );
}
