"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import {
  Typo<PERSON>,
  Card,
  CardBody,
  Button,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
  Switch,
} from "@material-tailwind/react";
import {
  Globe,
  ArrowLeft,
  Server,
  Shield,
  Lock,
  Mail,
  ExternalLink,
  RefreshCw,
  Zap,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import NameserverManager from "@/components/domains/NameserverManager";
import PrivacyProtectionManager from "@/components/domains/PrivacyProtectionManager";

export default function DomainDetailPage({ params }) {
  const { id } = params;
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");
  const [domain, setDomain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const router = useRouter();

  // Utility function to format Unix timestamps
  const formatDate = (unixTimestamp) => {
    if (!unixTimestamp) return "Not available";
    try {
      const date = new Date(parseInt(unixTimestamp) * 1000);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return "Invalid date";
    }
  };

  // Utility function to format domain status
  const formatStatus = (status) => {
    if (!status) return "unknown";
    return status.toLowerCase().replace(/([a-z])([A-Z])/g, "$1 $2");
  };

  // Utility function to safely get contact information
  const getContactInfo = (contact, field, fallback = "Not available") => {
    if (!contact || typeof contact !== "object") {
      return fallback;
    }
    return contact[field] || fallback;
  };

  // Utility function to check if contact exists and has data
  const hasContactData = (contact) => {
    return (
      contact && typeof contact === "object" && (contact.name || contact.email)
    );
  };

  useEffect(() => {
    const getDomainDetails = async () => {
      try {
        setLoading(true);

        // First, get the user's domains to find the domain name by ID
        const domainsRes = await domainMngService.getUserDomains();
        const userDomains = domainsRes.data?.domains || [];

        // Find the domain with the matching ID
        const userDomain = userDomains.find((d) => d.id === id);

        if (!userDomain) {
          console.error("Domain not found with ID:", id);
          setLoading(false);
          return;
        }

        console.log("Found user domain:", userDomain);

        // Try to get detailed information from the reseller API
        try {
          console.log("🔍 Fetching real domain details for:", userDomain.name);

          // Get real domain details from reseller API
          const detailsRes = await domainMngService.getDomainDetailsByName(
            userDomain.name,
            "All" // Get all available details
          );
          console.log(
            "✅ Real domain details from reseller API:",
            detailsRes.data
          );

          const apiDomain = detailsRes.data?.domain;

          // Debug privacy protection data
          console.log("🔒 [PRIVACY DEBUG] API Privacy Data:", {
            apiPrivacyProtection: apiDomain?.privacyProtection,
            userPrivacyProtection: userDomain.privacyProtection,
            apiPrivacyEnabled: apiDomain?.privacyProtection?.enabled,
            willUseApiData: apiDomain?.privacyProtection?.enabled !== undefined,
          });

          if (apiDomain) {
            // Use real data from reseller API
            const combinedDomain = {
              id: userDomain.id,
              name: apiDomain.domainName || userDomain.name,
              status: apiDomain.status || userDomain.status,
              registrationDate:
                apiDomain.registrationDate || userDomain.registrationDate,
              expiryDate: apiDomain.expiryDate || userDomain.expiryDate,
              autoRenew: apiDomain.autoRenew || userDomain.autoRenew || false,
              registrar: "ZTech Domains",

              // Use real nameservers from API
              nameservers:
                apiDomain.nameservers && apiDomain.nameservers.length > 0
                  ? apiDomain.nameservers
                  : userDomain.nameservers || [
                      "ns1.ztech",
                      "ns2.ztech",
                      "ns3.ztech",
                      "ns4.ztech",
                    ],

              // Use real privacy protection data from reseller API (prioritize API data)
              privacyProtection:
                apiDomain.privacyProtection?.enabled !== undefined
                  ? apiDomain.privacyProtection.enabled
                  : userDomain.privacyProtection || false,
              privacyProtectionDetails: apiDomain.privacyProtection,

              period: userDomain.period,
              price: userDomain.price,
              orderId: apiDomain.orderId || userDomain.orderId,
              orderStatus: apiDomain.orderStatus || userDomain.orderStatus,

              // Real contact details from API
              contacts: {
                registrant: apiDomain.contactDetails?.registrant
                  ? {
                      name: apiDomain.contactDetails.registrant.name,
                      email: apiDomain.contactDetails.registrant.emailaddr,
                      phone: `+${apiDomain.contactDetails.registrant.telnocc} ${apiDomain.contactDetails.registrant.telno}`,
                      address: `${apiDomain.contactDetails.registrant.address1}, ${apiDomain.contactDetails.registrant.city}, ${apiDomain.contactDetails.registrant.country} ${apiDomain.contactDetails.registrant.zip}`,
                      company: apiDomain.contactDetails.registrant.company,
                      contactId: apiDomain.contactDetails.registrant.contactid,
                    }
                  : {
                      name: "Contact information not available",
                      email: "Contact information not available",
                      phone: "Contact information not available",
                      address: "Contact information not available",
                    },
                admin: apiDomain.contactDetails?.admin
                  ? {
                      name: apiDomain.contactDetails.admin.name,
                      email: apiDomain.contactDetails.admin.emailaddr,
                      phone: `+${apiDomain.contactDetails.admin.telnocc} ${apiDomain.contactDetails.admin.telno}`,
                      address: `${apiDomain.contactDetails.admin.address1}, ${apiDomain.contactDetails.admin.city}, ${apiDomain.contactDetails.admin.country} ${apiDomain.contactDetails.admin.zip}`,
                      company: apiDomain.contactDetails.admin.company,
                      contactId: apiDomain.contactDetails.admin.contactid,
                    }
                  : {
                      name: "Contact information not available",
                      email: "Contact information not available",
                      phone: "Contact information not available",
                      address: "Contact information not available",
                    },
                technical: apiDomain.contactDetails?.tech
                  ? {
                      name: apiDomain.contactDetails.tech.name,
                      email: apiDomain.contactDetails.tech.emailaddr,
                      phone: `+${apiDomain.contactDetails.tech.telnocc} ${apiDomain.contactDetails.tech.telno}`,
                      address: `${apiDomain.contactDetails.tech.address1}, ${apiDomain.contactDetails.tech.city}, ${apiDomain.contactDetails.tech.country} ${apiDomain.contactDetails.tech.zip}`,
                      company: apiDomain.contactDetails.tech.company,
                      contactId: apiDomain.contactDetails.tech.contactid,
                    }
                  : {
                      name: "Contact information not available",
                      email: "Contact information not available",
                      phone: "Contact information not available",
                      address: "Contact information not available",
                    },
                billing: apiDomain.contactDetails?.billing
                  ? {
                      name: apiDomain.contactDetails.billing.name,
                      email: apiDomain.contactDetails.billing.emailaddr,
                      phone: `+${apiDomain.contactDetails.billing.telnocc} ${apiDomain.contactDetails.billing.telno}`,
                      address: `${apiDomain.contactDetails.billing.address1}, ${apiDomain.contactDetails.billing.city}, ${apiDomain.contactDetails.billing.country} ${apiDomain.contactDetails.billing.zip}`,
                      company: apiDomain.contactDetails.billing.company,
                      contactId: apiDomain.contactDetails.billing.contactid,
                    }
                  : {
                      name: "Contact information not available",
                      email: "Contact information not available",
                      phone: "Contact information not available",
                      address: "Contact information not available",
                    },
              },

              // Contact IDs for API operations
              contactIds: apiDomain.contacts,

              // Additional real data from API
              productCategory: apiDomain.productCategory,
              productKey: apiDomain.productKey,
              customerId: apiDomain.customerId,
              gdpr: apiDomain.gdpr,
              locks: apiDomain.locks,
              raaVerification: apiDomain.raaVerification,
              dnssec: apiDomain.dnssec,

              // Raw API response for debugging
              apiDetails: apiDomain,

              // Default DNS records (placeholder - would need separate API call)
              dnsRecords: [
                {
                  id: "rec1",
                  type: "A",
                  name: "@",
                  content: "DNS records available via separate API",
                  ttl: 14400,
                },
              ],
            };

            setDomain(combinedDomain);
          } else {
            throw new Error("No domain data received from API");
          }
        } catch (apiError) {
          console.warn("Could not fetch domain details from API:", apiError);

          // Fallback to user domain data only
          const fallbackDomain = {
            id: userDomain.id,
            name: userDomain.name,
            status: userDomain.status,
            registrationDate: userDomain.registrationDate,
            expiryDate: userDomain.expiryDate,
            autoRenew: userDomain.autoRenew,
            registrar: userDomain.registrar || "ZTech Domains",
            nameservers: userDomain.nameservers || [
              "ns1.ztech",
              "ns2.ztech",
              "ns3.ztech",
              "ns4.ztech",
            ],
            privacyProtection: userDomain.privacyProtection,
            period: userDomain.period,
            price: userDomain.price,
            orderId: userDomain.orderId,
            orderStatus: userDomain.orderStatus,
            contacts: {
              registrant: {
                name: "Contact information not available",
                email: "Contact information not available",
                phone: "Contact information not available",
                address: "Contact information not available",
              },
              admin: {
                name: "Contact information not available",
                email: "Contact information not available",
                phone: "Contact information not available",
                address: "Contact information not available",
              },
              technical: {
                name: "Contact information not available",
                email: "Contact information not available",
                phone: "Contact information not available",
                address: "Contact information not available",
              },
            },
            dnsRecords: [
              {
                id: "rec1",
                type: "A",
                name: "@",
                content: "DNS information not available",
                ttl: 14400,
              },
            ],
          };

          setDomain(fallbackDomain);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error getting domain details", error);
        setLoading(false);
      }
    };
    getDomainDetails();
  }, [id]);

  const handleAutoRenewToggle = async (value) => {
    try {
      // This would be replaced with actual API call when implemented
      // await domainMngService.toggleAutoRenewal(id, value);
      setDomain({ ...domain, autoRenew: value });
    } catch (error) {
      console.error("Error toggling auto renewal", error);
    }
  };

  const handlePrivacyToggle = async (value) => {
    try {
      console.log(
        `🔧 [PRIVACY] Toggling privacy protection for domain ${domain.name}:`,
        {
          from: domain.privacyProtection,
          to: value,
          orderId: domain.orderId,
        }
      );

      if (!domain.orderId) {
        console.error("🔧 [PRIVACY] ❌ No order ID available for domain");
        return;
      }

      // Call the modify privacy protection API
      const response = await domainMngService.modifyPrivacyProtection({
        orderId: domain.orderId,
        protectPrivacy: value,
        reason: `User ${
          value ? "enabled" : "disabled"
        } privacy protection via domain management panel`,
      });

      console.log(
        "🔧 [PRIVACY] ✅ Privacy protection updated successfully:",
        response.data
      );

      // Update local state
      setDomain({ ...domain, privacyProtection: value });

      // Show success message
      toast.success(
        `Privacy protection ${value ? "enabled" : "disabled"} successfully`
      );
    } catch (error) {
      console.error(
        "🔧 [PRIVACY] ❌ Error toggling privacy protection:",
        error
      );
      toast.error("Failed to update privacy protection. Please try again.");
    }
  };

  const handlePurchasePrivacy = async () => {
    try {
      console.log(
        `🛒 [PRIVACY] Purchasing privacy protection for domain ${domain.name}:`,
        {
          orderId: domain.orderId,
        }
      );

      if (!domain.orderId) {
        console.error("🛒 [PRIVACY] ❌ No order ID available for domain");
        toast.error(
          "Unable to purchase privacy protection. Order ID not found."
        );
        return;
      }

      // Call the purchase privacy protection API
      const response = await domainMngService.purchasePrivacyProtection({
        orderId: domain.orderId,
        invoiceOption: "NoInvoice", // Use NoInvoice to deduct from account balance
        discountAmount: 0,
      });

      console.log(
        "🛒 [PRIVACY] ✅ Privacy protection purchased successfully:",
        response.data
      );

      // Update local state - privacy protection is now purchased and enabled
      setDomain({
        ...domain,
        privacyProtection: true,
        privacyProtectionDetails: {
          ...domain.privacyProtectionDetails,
          purchased: true,
          enabled: true,
        },
      });

      // Show success message
      toast.success("Privacy protection purchased and enabled successfully!");
    } catch (error) {
      console.error(
        "🛒 [PRIVACY] ❌ Error purchasing privacy protection:",
        error
      );
      toast.error("Failed to purchase privacy protection. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Globe className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="text-gray-600">
            {t("loading")}...
          </Typography>
        </div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-8">
        <Typography variant="h4" className="text-gray-800 font-bold mb-2">
          {t("domain_not_found", { defaultValue: "Domain Not Found" })}
        </Typography>
        <Button
          className="mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <Button
          variant="text"
          className="mb-6 text-blue-600 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
              <Globe className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <Typography
                variant="h1"
                className="text-2xl font-bold text-gray-800"
              >
                {domain.name}
              </Typography>
              <div className="flex items-center mt-1">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 ${
                    domain.status?.toLowerCase() === "active"
                      ? "bg-green-100 text-green-800"
                      : domain.status?.toLowerCase() === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : domain.status?.toLowerCase() === "expired"
                      ? "bg-red-100 text-red-800"
                      : domain.status?.toLowerCase() === "failed"
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {dt(domain.status?.toLowerCase() || "unknown")}
                </span>
                <Typography className="text-sm text-gray-500">
                  {dt("registrar")}: {domain.registrar}
                </Typography>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outlined"
              className="border-purple-600 text-purple-600 hover:bg-purple-50 flex items-center gap-2"
              onClick={() => router.push(`/client/domains/${id}/dns`)}
            >
              <Zap className="h-4 w-4" />
              {dt("manage_dns_records")}
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} className="mb-8">
          <TabsHeader
            className="bg-gray-100 rounded-lg p-1"
            indicatorProps={{
              className: "bg-white shadow-md rounded-md",
            }}
          >
            <Tab
              value="overview"
              onClick={() => setActiveTab("overview")}
              className={activeTab === "overview" ? "text-blue-600" : ""}
            >
              {t("overview", { defaultValue: "Overview" })}
            </Tab>
            <Tab
              value="contacts"
              onClick={() => setActiveTab("contacts")}
              className={activeTab === "contacts" ? "text-blue-600" : ""}
            >
              {dt("domain_contacts")}
            </Tab>
            <Tab
              value="privacy"
              onClick={() => setActiveTab("privacy")}
              className={activeTab === "privacy" ? "text-blue-600" : ""}
            >
              {t("privacy", { defaultValue: "Privacy" })}
            </Tab>
          </TabsHeader>
          <TabsBody>
            <TabPanel value="overview" className="p-0 mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <CardBody className="p-6">
                    <Typography className="text-lg font-medium text-gray-900 mb-4">
                      {dt("domain_details")}
                    </Typography>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("domain_name")}
                        </Typography>
                        <Typography className="font-medium">
                          {domain.name}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          Order ID
                        </Typography>
                        <Typography className="font-medium text-blue-600">
                          #{domain.orderId}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("status")}
                        </Typography>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                            domain.status?.toLowerCase() === "active"
                              ? "bg-green-100 text-green-800"
                              : domain.status?.toLowerCase() === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : domain.status?.toLowerCase() === "expired"
                              ? "bg-red-100 text-red-800"
                              : domain.status?.toLowerCase() === "failed"
                              ? "bg-red-100 text-red-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {dt(domain.status?.toLowerCase() || "unknown")}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("registration_date")}
                        </Typography>
                        <Typography className="font-medium text-sm">
                          {formatDate(domain.registrationDate)}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("expiry_date")}
                        </Typography>
                        <Typography className="font-medium text-sm">
                          {formatDate(domain.expiryDate)}
                        </Typography>
                      </div>
                      <div className="flex justify-between items-center">
                        <Typography className="text-sm text-gray-500">
                          {dt("auto_renew")}
                        </Typography>
                        <Switch
                          checked={domain.autoRenew}
                          onChange={(e) =>
                            handleAutoRenewToggle(e.target.checked)
                          }
                          color="blue"
                          disabled={true}
                        />
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Security & Protection */}
                <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <CardBody className="p-6">
                    <Typography className="text-lg font-medium text-gray-900 mb-4">
                      Security & Protection
                    </Typography>
                    <div className="space-y-4">
                      <div className="flex justify-between items-start">
                        <Typography className="text-sm text-gray-500">
                          {dt("whois_privacy")}
                        </Typography>
                        <div className="flex flex-col items-end gap-2">
                          {/* Check if privacy protection service is purchased */}
                          {domain.privacyProtectionDetails?.purchased !==
                          false ? (
                            // Privacy protection is purchased - show toggle
                            <div className="flex items-center gap-2">
                              <Switch
                                checked={domain.privacyProtection}
                                onChange={(e) =>
                                  handlePrivacyToggle(e.target.checked)
                                }
                                color="blue"
                              />
                              <span
                                className={`text-xs px-2 py-1 rounded ${
                                  domain.privacyProtection
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-600"
                                }`}
                              >
                                {domain.privacyProtection
                                  ? "Enabled"
                                  : "Disabled"}
                              </span>
                            </div>
                          ) : (
                            // Privacy protection not purchased - show purchase option
                            <div className="flex flex-col items-end gap-1">
                              <span className="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded">
                                Not Purchased
                              </span>
                              <Button
                                size="sm"
                                color="blue"
                                variant="outlined"
                                className="text-xs px-3 py-1"
                                onClick={() => handlePurchasePrivacy()}
                              >
                                Purchase Privacy Protection
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>

                      {domain.orderStatus &&
                        Array.isArray(domain.orderStatus) &&
                        domain.orderStatus.length > 0 && (
                          <div className="flex justify-between items-start">
                            <Typography className="text-sm text-gray-500">
                              Domain Locks
                            </Typography>
                            <div className="flex flex-col gap-1">
                              {domain.orderStatus.map((lock, index) => (
                                <span
                                  key={index}
                                  className="text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded"
                                >
                                  {lock}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                      {domain.domainStatus &&
                        Array.isArray(domain.domainStatus) &&
                        domain.domainStatus.length > 0 && (
                          <div className="flex justify-between items-start">
                            <Typography className="text-sm text-gray-500">
                              Registry Status
                            </Typography>
                            <div className="flex flex-col gap-1">
                              {domain.domainStatus.map((status, index) => (
                                <span
                                  key={index}
                                  className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded"
                                >
                                  {status}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                      {domain.raaVerification?.status && (
                        <div className="flex justify-between items-center">
                          <Typography className="text-sm text-gray-500">
                            RAA Verification
                          </Typography>
                          <span
                            className={`text-xs px-2 py-1 rounded ${
                              domain.raaVerification.status === "Verified"
                                ? "bg-green-100 text-green-800"
                                : domain.raaVerification.status === "Pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {domain.raaVerification.status}
                          </span>
                        </div>
                      )}

                      {domain.gdpr?.enabled !== undefined && (
                        <div className="flex justify-between items-center">
                          <Typography className="text-sm text-gray-500">
                            GDPR Protection
                          </Typography>
                          <span
                            className={`text-xs px-2 py-1 rounded ${
                              domain.gdpr.enabled === "true"
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-600"
                            }`}
                          >
                            {domain.gdpr.enabled === "true"
                              ? "Enabled"
                              : "Disabled"}
                          </span>
                        </div>
                      )}

                      {/* Registration Error Display */}
                      {(domain.status?.toLowerCase() === "failed" ||
                        domain.orderStatus === "FAILED" ||
                        domain.registrationError) && (
                        <div className="flex justify-between items-start">
                          <Typography className="text-sm text-gray-500">
                            Registration Status
                          </Typography>
                          <div className="flex flex-col gap-1">
                            <span className="text-xs px-2 py-1 bg-red-100 text-red-800 rounded">
                              Registration Failed
                            </span>
                            {domain.registrationError && (
                              <span className="text-xs px-2 py-1 bg-red-50 text-red-700 rounded">
                                {domain.registrationError}
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardBody>
                </Card>

                {/* Nameservers */}
                <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <CardBody className="p-6">
                    <Typography className="text-lg font-medium text-gray-900 mb-4">
                      {dt("nameservers")}
                    </Typography>
                    <div className="space-y-4">
                      {domain.nameservers &&
                      Array.isArray(domain.nameservers) &&
                      domain.nameservers.length > 0 ? (
                        domain.nameservers.map((ns, index) => (
                          <div
                            key={index}
                            className="flex justify-between items-center"
                          >
                            <Typography className="text-sm text-gray-500">
                              NS {index + 1}
                            </Typography>
                            <Typography className="font-medium">
                              {ns}
                            </Typography>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-4">
                          <Typography className="text-sm text-gray-500">
                            {domain.status?.toLowerCase() === "failed" ||
                            domain.orderStatus === "FAILED"
                              ? "Nameservers not available - Registration failed"
                              : "No nameservers configured"}
                          </Typography>
                        </div>
                      )}
                      <div className="mt-6">
                        <Button
                          variant="outlined"
                          className="w-full border-blue-600 text-blue-600 hover:bg-blue-50"
                          onClick={() => setActiveTab("dns")}
                        >
                          {dt("update_nameservers")}
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </TabPanel>

            <TabPanel value="contacts" className="p-0 mt-6">
              {/* Domain Contacts Tab Content */}
              <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-6">
                  <Typography className="text-lg font-medium text-gray-900 mb-6">
                    {dt("domain_contacts")}
                  </Typography>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("registrant", {
                          defaultValue: "Registrant Contact",
                        })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        {hasContactData(domain.contacts?.registrant) ? (
                          <>
                            <Typography className="font-medium text-gray-900">
                              {getContactInfo(
                                domain.contacts.registrant,
                                "name"
                              )}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.registrant,
                              "company",
                              null
                            ) && (
                              <Typography className="text-sm text-blue-600 font-medium">
                                {getContactInfo(
                                  domain.contacts.registrant,
                                  "company"
                                )}
                              </Typography>
                            )}
                            <Typography className="text-sm text-gray-600 mt-2">
                              📧{" "}
                              {getContactInfo(
                                domain.contacts.registrant,
                                "email"
                              )}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📞{" "}
                              {getContactInfo(
                                domain.contacts.registrant,
                                "phone"
                              )}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📍{" "}
                              {getContactInfo(
                                domain.contacts.registrant,
                                "address"
                              )}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.registrant,
                              "contactId",
                              null
                            ) && (
                              <Typography className="text-xs text-gray-400 mt-2">
                                ID:{" "}
                                {getContactInfo(
                                  domain.contacts.registrant,
                                  "contactId"
                                )}
                              </Typography>
                            )}
                          </>
                        ) : (
                          <Typography className="text-sm text-gray-500 italic">
                            Contact information not available from reseller API
                          </Typography>
                        )}
                      </div>
                    </div>
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("admin", { defaultValue: "Administrative Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        {hasContactData(domain.contacts?.admin) ? (
                          <>
                            <Typography className="font-medium text-gray-900">
                              {getContactInfo(domain.contacts.admin, "name")}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.admin,
                              "company",
                              null
                            ) && (
                              <Typography className="text-sm text-blue-600 font-medium">
                                {getContactInfo(
                                  domain.contacts.admin,
                                  "company"
                                )}
                              </Typography>
                            )}
                            <Typography className="text-sm text-gray-600 mt-2">
                              📧{" "}
                              {getContactInfo(domain.contacts.admin, "email")}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📞{" "}
                              {getContactInfo(domain.contacts.admin, "phone")}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📍{" "}
                              {getContactInfo(domain.contacts.admin, "address")}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.admin,
                              "contactId",
                              null
                            ) && (
                              <Typography className="text-xs text-gray-400 mt-2">
                                ID:{" "}
                                {getContactInfo(
                                  domain.contacts.admin,
                                  "contactId"
                                )}
                              </Typography>
                            )}
                          </>
                        ) : (
                          <Typography className="text-sm text-gray-500 italic">
                            Contact information not available from reseller API
                          </Typography>
                        )}
                      </div>
                    </div>
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("technical", { defaultValue: "Technical Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        {hasContactData(domain.contacts?.technical) ? (
                          <>
                            <Typography className="font-medium text-gray-900">
                              {getContactInfo(
                                domain.contacts.technical,
                                "name"
                              )}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.technical,
                              "company",
                              null
                            ) && (
                              <Typography className="text-sm text-blue-600 font-medium">
                                {getContactInfo(
                                  domain.contacts.technical,
                                  "company"
                                )}
                              </Typography>
                            )}
                            <Typography className="text-sm text-gray-600 mt-2">
                              📧{" "}
                              {getContactInfo(
                                domain.contacts.technical,
                                "email"
                              )}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📞{" "}
                              {getContactInfo(
                                domain.contacts.technical,
                                "phone"
                              )}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📍{" "}
                              {getContactInfo(
                                domain.contacts.technical,
                                "address"
                              )}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.technical,
                              "contactId",
                              null
                            ) && (
                              <Typography className="text-xs text-gray-400 mt-2">
                                ID:{" "}
                                {getContactInfo(
                                  domain.contacts.technical,
                                  "contactId"
                                )}
                              </Typography>
                            )}
                          </>
                        ) : (
                          <Typography className="text-sm text-gray-500 italic">
                            Contact information not available from reseller API
                          </Typography>
                        )}
                      </div>
                    </div>

                    {/* Add Billing Contact */}
                    <div>
                      <Typography className="font-medium text-gray-900 mb-2">
                        {t("billing", { defaultValue: "Billing Contact" })}
                      </Typography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        {hasContactData(domain.contacts?.billing) ? (
                          <>
                            <Typography className="font-medium text-gray-900">
                              {getContactInfo(domain.contacts.billing, "name")}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.billing,
                              "company",
                              null
                            ) && (
                              <Typography className="text-sm text-blue-600 font-medium">
                                {getContactInfo(
                                  domain.contacts.billing,
                                  "company"
                                )}
                              </Typography>
                            )}
                            <Typography className="text-sm text-gray-600 mt-2">
                              📧{" "}
                              {getContactInfo(domain.contacts.billing, "email")}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📞{" "}
                              {getContactInfo(domain.contacts.billing, "phone")}
                            </Typography>
                            <Typography className="text-sm text-gray-600">
                              📍{" "}
                              {getContactInfo(
                                domain.contacts.billing,
                                "address"
                              )}
                            </Typography>
                            {getContactInfo(
                              domain.contacts.billing,
                              "contactId",
                              null
                            ) && (
                              <Typography className="text-xs text-gray-400 mt-2">
                                ID:{" "}
                                {getContactInfo(
                                  domain.contacts.billing,
                                  "contactId"
                                )}
                              </Typography>
                            )}
                          </>
                        ) : (
                          <Typography className="text-sm text-gray-500 italic">
                            Contact information not available from reseller API
                          </Typography>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="mt-6">
                    <Button
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      onClick={() =>
                        router.push(`/client/domains/${id}/contacts`)
                      }
                    >
                      {dt("update_contacts")}
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </TabPanel>

            <TabPanel value="privacy" className="p-0 mt-6">
              {/* Privacy Protection Tab Content */}
              <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-6">
                  <Typography className="text-lg font-medium text-gray-900 mb-6">
                    {t("privacy", { defaultValue: "Privacy Protection" })}
                  </Typography>
                  <div className="text-center py-8">
                    <Shield className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <Typography className="text-gray-500 mb-4">
                      {t("privacy_content_coming_soon", {
                        defaultValue:
                          "Privacy protection settings will be available soon.",
                      })}
                    </Typography>
                    <Typography className="text-sm text-gray-400">
                      {t("privacy_description", {
                        defaultValue:
                          "Manage your domain privacy protection and WHOIS information visibility.",
                      })}
                    </Typography>
                  </div>
                </CardBody>
              </Card>
            </TabPanel>
          </TabsBody>
        </Tabs>
      </div>
    </div>
  );
}
