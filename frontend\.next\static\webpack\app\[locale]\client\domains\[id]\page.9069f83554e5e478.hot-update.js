"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    var _domain_status, _domain_status1, _domain_status2, _domain_status3, _domain_status4, _domain_status5, _domain_status6, _domain_status7, _domain_status8, _domain_status9, _domain_privacyProtectionDetails, _domain_raaVerification, _domain_gdpr, _domain_status10, _domain_status11, _domain_contacts, _domain_contacts1, _domain_contacts2, _domain_contacts3;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Utility function to format Unix timestamps\n    const formatDate = (unixTimestamp)=>{\n        if (!unixTimestamp) return \"Not available\";\n        try {\n            const date = new Date(parseInt(unixTimestamp) * 1000);\n            return date.toLocaleDateString(\"en-US\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } catch (error) {\n            return \"Invalid date\";\n        }\n    };\n    // Utility function to format domain status\n    const formatStatus = (status)=>{\n        if (!status) return \"unknown\";\n        return status.toLowerCase().replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    // Utility function to safely get contact information\n    const getContactInfo = function(contact, field) {\n        let fallback = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"Not available\";\n        if (!contact || typeof contact !== \"object\") {\n            return fallback;\n        }\n        return contact[field] || fallback;\n    };\n    // Utility function to check if contact exists and has data\n    const hasContactData = (contact)=>{\n        return contact && typeof contact === \"object\" && (contact.name || contact.email);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Try to get detailed information from the reseller API\n                try {\n                    var _detailsRes_data, _apiDomain_privacyProtection, _apiDomain_privacyProtection1;\n                    console.log(\"\\uD83D\\uDD0D Fetching real domain details for:\", userDomain.name);\n                    // Get real domain details from reseller API\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details\n                    );\n                    console.log(\"✅ Real domain details from reseller API:\", detailsRes.data);\n                    const apiDomain = (_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain;\n                    // Debug privacy protection data\n                    console.log(\"\\uD83D\\uDD12 [PRIVACY DEBUG] API Privacy Data:\", {\n                        apiPrivacyProtection: apiDomain === null || apiDomain === void 0 ? void 0 : apiDomain.privacyProtection,\n                        userPrivacyProtection: userDomain.privacyProtection,\n                        apiPrivacyEnabled: apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection === void 0 ? void 0 : _apiDomain_privacyProtection.enabled,\n                        willUseApiData: (apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection1 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection1 === void 0 ? void 0 : _apiDomain_privacyProtection1.enabled) !== undefined\n                    });\n                    if (apiDomain) {\n                        var _apiDomain_privacyProtection2, _apiDomain_contactDetails, _apiDomain_contactDetails1, _apiDomain_contactDetails2, _apiDomain_contactDetails3;\n                        // Use real data from reseller API\n                        const combinedDomain = {\n                            id: userDomain.id,\n                            name: apiDomain.domainName || userDomain.name,\n                            status: apiDomain.status || userDomain.status,\n                            registrationDate: apiDomain.registrationDate || userDomain.registrationDate,\n                            expiryDate: apiDomain.expiryDate || userDomain.expiryDate,\n                            autoRenew: apiDomain.autoRenew || userDomain.autoRenew || false,\n                            registrar: \"ZTech Domains\",\n                            // Use real nameservers from API\n                            nameservers: apiDomain.nameservers && apiDomain.nameservers.length > 0 ? apiDomain.nameservers : userDomain.nameservers || [\n                                \"ns1.ztech\",\n                                \"ns2.ztech\",\n                                \"ns3.ztech\",\n                                \"ns4.ztech\"\n                            ],\n                            // Use real privacy protection data from reseller API (prioritize API data)\n                            privacyProtection: ((_apiDomain_privacyProtection2 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection2 === void 0 ? void 0 : _apiDomain_privacyProtection2.enabled) !== undefined ? apiDomain.privacyProtection.enabled : userDomain.privacyProtection || false,\n                            privacyProtectionDetails: apiDomain.privacyProtection,\n                            period: userDomain.period,\n                            price: userDomain.price,\n                            orderId: apiDomain.orderId || userDomain.orderId,\n                            orderStatus: apiDomain.orderStatus || userDomain.orderStatus,\n                            // Real contact details from API\n                            contacts: {\n                                registrant: ((_apiDomain_contactDetails = apiDomain.contactDetails) === null || _apiDomain_contactDetails === void 0 ? void 0 : _apiDomain_contactDetails.registrant) ? {\n                                    name: apiDomain.contactDetails.registrant.name,\n                                    email: apiDomain.contactDetails.registrant.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.registrant.telnocc, \" \").concat(apiDomain.contactDetails.registrant.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.registrant.address1, \", \").concat(apiDomain.contactDetails.registrant.city, \", \").concat(apiDomain.contactDetails.registrant.country, \" \").concat(apiDomain.contactDetails.registrant.zip),\n                                    company: apiDomain.contactDetails.registrant.company,\n                                    contactId: apiDomain.contactDetails.registrant.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                admin: ((_apiDomain_contactDetails1 = apiDomain.contactDetails) === null || _apiDomain_contactDetails1 === void 0 ? void 0 : _apiDomain_contactDetails1.admin) ? {\n                                    name: apiDomain.contactDetails.admin.name,\n                                    email: apiDomain.contactDetails.admin.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.admin.telnocc, \" \").concat(apiDomain.contactDetails.admin.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.admin.address1, \", \").concat(apiDomain.contactDetails.admin.city, \", \").concat(apiDomain.contactDetails.admin.country, \" \").concat(apiDomain.contactDetails.admin.zip),\n                                    company: apiDomain.contactDetails.admin.company,\n                                    contactId: apiDomain.contactDetails.admin.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                technical: ((_apiDomain_contactDetails2 = apiDomain.contactDetails) === null || _apiDomain_contactDetails2 === void 0 ? void 0 : _apiDomain_contactDetails2.tech) ? {\n                                    name: apiDomain.contactDetails.tech.name,\n                                    email: apiDomain.contactDetails.tech.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.tech.telnocc, \" \").concat(apiDomain.contactDetails.tech.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.tech.address1, \", \").concat(apiDomain.contactDetails.tech.city, \", \").concat(apiDomain.contactDetails.tech.country, \" \").concat(apiDomain.contactDetails.tech.zip),\n                                    company: apiDomain.contactDetails.tech.company,\n                                    contactId: apiDomain.contactDetails.tech.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                billing: ((_apiDomain_contactDetails3 = apiDomain.contactDetails) === null || _apiDomain_contactDetails3 === void 0 ? void 0 : _apiDomain_contactDetails3.billing) ? {\n                                    name: apiDomain.contactDetails.billing.name,\n                                    email: apiDomain.contactDetails.billing.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.billing.telnocc, \" \").concat(apiDomain.contactDetails.billing.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.billing.address1, \", \").concat(apiDomain.contactDetails.billing.city, \", \").concat(apiDomain.contactDetails.billing.country, \" \").concat(apiDomain.contactDetails.billing.zip),\n                                    company: apiDomain.contactDetails.billing.company,\n                                    contactId: apiDomain.contactDetails.billing.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                }\n                            },\n                            // Contact IDs for API operations\n                            contactIds: apiDomain.contacts,\n                            // Additional real data from API\n                            productCategory: apiDomain.productCategory,\n                            productKey: apiDomain.productKey,\n                            customerId: apiDomain.customerId,\n                            gdpr: apiDomain.gdpr,\n                            locks: apiDomain.locks,\n                            raaVerification: apiDomain.raaVerification,\n                            dnssec: apiDomain.dnssec,\n                            // Raw API response for debugging\n                            apiDetails: apiDomain,\n                            // Default DNS records (placeholder - would need separate API call)\n                            dnsRecords: [\n                                {\n                                    id: \"rec1\",\n                                    type: \"A\",\n                                    name: \"@\",\n                                    content: \"DNS records available via separate API\",\n                                    ttl: 14400\n                                }\n                            ]\n                        };\n                        setDomain(combinedDomain);\n                    } else {\n                        throw new Error(\"No domain data received from API\");\n                    }\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"ns1.ztech\",\n                            \"ns2.ztech\",\n                            \"ns3.ztech\",\n                            \"ns4.ztech\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            technical: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 14400\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] Toggling privacy protection for domain \".concat(domain.name, \":\"), {\n                from: domain.privacyProtection,\n                to: value,\n                orderId: domain.orderId\n            });\n            if (!domain.orderId) {\n                console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ No order ID available for domain\");\n                return;\n            }\n            // Call the modify privacy protection API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].modifyPrivacyProtection({\n                orderId: domain.orderId,\n                protectPrivacy: value,\n                reason: \"User \".concat(value ? \"enabled\" : \"disabled\", \" privacy protection via domain management panel\")\n            });\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] ✅ Privacy protection updated successfully:\", response.data);\n            // Update local state\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n            // Show success message\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Privacy protection \".concat(value ? \"enabled\" : \"disabled\", \" successfully\"));\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ Error toggling privacy protection:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to update privacy protection. Please try again.\");\n        }\n    };\n    const handlePurchasePrivacy = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDED2 [PRIVACY] Purchasing privacy protection for domain \".concat(domain.name, \":\"), {\n                orderId: domain.orderId\n            });\n            if (!domain.orderId) {\n                console.error(\"\\uD83D\\uDED2 [PRIVACY] ❌ No order ID available for domain\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Unable to purchase privacy protection. Order ID not found.\");\n                return;\n            }\n            // Call the purchase privacy protection API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].purchasePrivacyProtection({\n                orderId: domain.orderId,\n                invoiceOption: \"NoInvoice\",\n                discountAmount: 0\n            });\n            console.log(\"\\uD83D\\uDED2 [PRIVACY] ✅ Privacy protection purchased successfully:\", response.data);\n            // Update local state - privacy protection is now purchased and enabled\n            setDomain({\n                ...domain,\n                privacyProtection: true,\n                privacyProtectionDetails: {\n                    ...domain.privacyProtectionDetails,\n                    purchased: true,\n                    enabled: true\n                }\n            });\n            // Show success message\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Privacy protection purchased and enabled successfully!\");\n        } catch (error) {\n            console.error(\"\\uD83D\\uDED2 [PRIVACY] ❌ Error purchasing privacy protection:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to purchase privacy protection. Please try again.\");\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 391,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 406,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(((_domain_status = domain.status) === null || _domain_status === void 0 ? void 0 : _domain_status.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status1 = domain.status) === null || _domain_status1 === void 0 ? void 0 : _domain_status1.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status2 = domain.status) === null || _domain_status2 === void 0 ? void 0 : _domain_status2.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status3 = domain.status) === null || _domain_status3 === void 0 ? void 0 : _domain_status3.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(((_domain_status4 = domain.status) === null || _domain_status4 === void 0 ? void 0 : _domain_status4.toLowerCase()) || \"unknown\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        t(\"visit_website\", {\n                                            defaultValue: \"Visit Website\"\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Order ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                domain.orderId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(((_domain_status5 = domain.status) === null || _domain_status5 === void 0 ? void 0 : _domain_status5.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status6 = domain.status) === null || _domain_status6 === void 0 ? void 0 : _domain_status6.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status7 = domain.status) === null || _domain_status7 === void 0 ? void 0 : _domain_status7.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status8 = domain.status) === null || _domain_status8 === void 0 ? void 0 : _domain_status8.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(((_domain_status9 = domain.status) === null || _domain_status9 === void 0 ? void 0 : _domain_status9.toLowerCase()) || \"unknown\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 566,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.registrationDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.expiryDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: \"Security & Protection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-end gap-2\",\n                                                                            children: ((_domain_privacyProtectionDetails = domain.privacyProtectionDetails) === null || _domain_privacyProtectionDetails === void 0 ? void 0 : _domain_privacyProtectionDetails.purchased) !== false ? // Privacy protection is purchased - show toggle\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        checked: domain.privacyProtection,\n                                                                                        onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                                        color: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 614,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 rounded \".concat(domain.privacyProtection ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                                        children: domain.privacyProtection ? \"Enabled\" : \"Disabled\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 621,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 613,\n                                                                                columnNumber: 29\n                                                                            }, this) : // Privacy protection not purchased - show purchase option\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col items-end gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded\",\n                                                                                        children: \"Not Purchased\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 631,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        color: \"blue\",\n                                                                                        variant: \"outlined\",\n                                                                                        className: \"text-xs px-3 py-1\",\n                                                                                        onClick: ()=>handlePurchasePrivacy(),\n                                                                                        children: \"Purchase Privacy Protection\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 634,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 609,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderStatus && Array.isArray(domain.orderStatus) && domain.orderStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Domain Locks\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.orderStatus.map((lock, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded\",\n                                                                                    children: lock\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 655,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 653,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.domainStatus && Array.isArray(domain.domainStatus) && domain.domainStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registry Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.domainStatus.map((status, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                                                    children: status\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 670,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_raaVerification = domain.raaVerification) === null || _domain_raaVerification === void 0 ? void 0 : _domain_raaVerification.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"RAA Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.raaVerification.status === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerification.status === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerification.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_gdpr = domain.gdpr) === null || _domain_gdpr === void 0 ? void 0 : _domain_gdpr.enabled) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"GDPR Protection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.gdpr.enabled === \"true\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                            children: domain.gdpr.enabled === \"true\" ? \"Enabled\" : \"Disabled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                (((_domain_status10 = domain.status) === null || _domain_status10 === void 0 ? void 0 : _domain_status10.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" || domain.registrationError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registration Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 713,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-100 text-red-800 rounded\",\n                                                                                    children: \"Registration Failed\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 717,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                domain.registrationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-50 text-red-700 rounded\",\n                                                                                    children: domain.registrationError\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 721,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 716,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers && Array.isArray(domain.nameservers) && domain.nameservers.length > 0 ? domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 745,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 748,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 27\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_domain_status11 = domain.status) === null || _domain_status11 === void 0 ? void 0 : _domain_status11.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" ? \"Nameservers not available - Registration failed\" : \"No nameservers configured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: (updatedDomain)=>setDomain(updatedDomain)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: dt(\"manage_dns_records\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outlined\",\n                                                                    className: \"border-purple-600 text-purple-600 hover:bg-purple-50 flex items-center gap-2\",\n                                                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns\")),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 800,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Modern DNS Manager\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                                    children: t(\"add_record\", {\n                                                                        defaultValue: \"Add Record\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts = domain.contacts) === null || _domain_contacts === void 0 ? void 0 : _domain_contacts.registrant) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 834,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 838,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 845,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 848,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 825,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts1 = domain.contacts) === null || _domain_contacts1 === void 0 ? void 0 : _domain_contacts1.admin) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 875,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.admin, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.admin, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 882,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.admin, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 885,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.admin, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 889,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts2 = domain.contacts) === null || _domain_contacts2 === void 0 ? void 0 : _domain_contacts2.technical) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 908,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 912,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.technical, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.technical, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 919,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.technical, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 922,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.technical, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 926,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 932,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts3 = domain.contacts) === null || _domain_contacts3 === void 0 ? void 0 : _domain_contacts3.billing) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 947,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 951,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.billing, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 955,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.billing, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 958,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.billing, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 961,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.billing, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 965,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 971,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 944,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 1000,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 423,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 422,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});