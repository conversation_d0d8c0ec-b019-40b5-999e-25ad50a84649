"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    var _domain_status, _domain_status1, _domain_status2, _domain_status3, _domain_status4, _domain_status5, _domain_status6, _domain_status7, _domain_status8, _domain_status9, _domain_privacyProtectionDetails, _domain_raaVerification, _domain_gdpr, _domain_status10, _domain_status11, _domain_contacts, _domain_contacts1, _domain_contacts2, _domain_contacts3;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Utility function to format Unix timestamps\n    const formatDate = (unixTimestamp)=>{\n        if (!unixTimestamp) return \"Not available\";\n        try {\n            const date = new Date(parseInt(unixTimestamp) * 1000);\n            return date.toLocaleDateString(\"en-US\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } catch (error) {\n            return \"Invalid date\";\n        }\n    };\n    // Utility function to format domain status\n    const formatStatus = (status)=>{\n        if (!status) return \"unknown\";\n        return status.toLowerCase().replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    // Utility function to safely get contact information\n    const getContactInfo = function(contact, field) {\n        let fallback = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"Not available\";\n        if (!contact || typeof contact !== \"object\") {\n            return fallback;\n        }\n        return contact[field] || fallback;\n    };\n    // Utility function to check if contact exists and has data\n    const hasContactData = (contact)=>{\n        return contact && typeof contact === \"object\" && (contact.name || contact.email);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Try to get detailed information from the reseller API\n                try {\n                    var _detailsRes_data, _apiDomain_privacyProtection, _apiDomain_privacyProtection1;\n                    console.log(\"\\uD83D\\uDD0D Fetching real domain details for:\", userDomain.name);\n                    // Get real domain details from reseller API\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details\n                    );\n                    console.log(\"✅ Real domain details from reseller API:\", detailsRes.data);\n                    const apiDomain = (_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain;\n                    // Debug privacy protection data\n                    console.log(\"\\uD83D\\uDD12 [PRIVACY DEBUG] API Privacy Data:\", {\n                        apiPrivacyProtection: apiDomain === null || apiDomain === void 0 ? void 0 : apiDomain.privacyProtection,\n                        userPrivacyProtection: userDomain.privacyProtection,\n                        apiPrivacyEnabled: apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection === void 0 ? void 0 : _apiDomain_privacyProtection.enabled,\n                        willUseApiData: (apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection1 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection1 === void 0 ? void 0 : _apiDomain_privacyProtection1.enabled) !== undefined\n                    });\n                    if (apiDomain) {\n                        var _apiDomain_privacyProtection2, _apiDomain_contactDetails, _apiDomain_contactDetails1, _apiDomain_contactDetails2, _apiDomain_contactDetails3;\n                        // Use real data from reseller API\n                        const combinedDomain = {\n                            id: userDomain.id,\n                            name: apiDomain.domainName || userDomain.name,\n                            status: apiDomain.status || userDomain.status,\n                            registrationDate: apiDomain.registrationDate || userDomain.registrationDate,\n                            expiryDate: apiDomain.expiryDate || userDomain.expiryDate,\n                            autoRenew: apiDomain.autoRenew || userDomain.autoRenew || false,\n                            registrar: \"ZTech Domains\",\n                            // Use real nameservers from API\n                            nameservers: apiDomain.nameservers && apiDomain.nameservers.length > 0 ? apiDomain.nameservers : userDomain.nameservers || [\n                                \"ns1.ztech\",\n                                \"ns2.ztech\",\n                                \"ns3.ztech\",\n                                \"ns4.ztech\"\n                            ],\n                            // Use real privacy protection data from reseller API (prioritize API data)\n                            privacyProtection: ((_apiDomain_privacyProtection2 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection2 === void 0 ? void 0 : _apiDomain_privacyProtection2.enabled) !== undefined ? apiDomain.privacyProtection.enabled : userDomain.privacyProtection || false,\n                            privacyProtectionDetails: apiDomain.privacyProtection,\n                            period: userDomain.period,\n                            price: userDomain.price,\n                            orderId: apiDomain.orderId || userDomain.orderId,\n                            orderStatus: apiDomain.orderStatus || userDomain.orderStatus,\n                            // Real contact details from API\n                            contacts: {\n                                registrant: ((_apiDomain_contactDetails = apiDomain.contactDetails) === null || _apiDomain_contactDetails === void 0 ? void 0 : _apiDomain_contactDetails.registrant) ? {\n                                    name: apiDomain.contactDetails.registrant.name,\n                                    email: apiDomain.contactDetails.registrant.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.registrant.telnocc, \" \").concat(apiDomain.contactDetails.registrant.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.registrant.address1, \", \").concat(apiDomain.contactDetails.registrant.city, \", \").concat(apiDomain.contactDetails.registrant.country, \" \").concat(apiDomain.contactDetails.registrant.zip),\n                                    company: apiDomain.contactDetails.registrant.company,\n                                    contactId: apiDomain.contactDetails.registrant.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                admin: ((_apiDomain_contactDetails1 = apiDomain.contactDetails) === null || _apiDomain_contactDetails1 === void 0 ? void 0 : _apiDomain_contactDetails1.admin) ? {\n                                    name: apiDomain.contactDetails.admin.name,\n                                    email: apiDomain.contactDetails.admin.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.admin.telnocc, \" \").concat(apiDomain.contactDetails.admin.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.admin.address1, \", \").concat(apiDomain.contactDetails.admin.city, \", \").concat(apiDomain.contactDetails.admin.country, \" \").concat(apiDomain.contactDetails.admin.zip),\n                                    company: apiDomain.contactDetails.admin.company,\n                                    contactId: apiDomain.contactDetails.admin.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                technical: ((_apiDomain_contactDetails2 = apiDomain.contactDetails) === null || _apiDomain_contactDetails2 === void 0 ? void 0 : _apiDomain_contactDetails2.tech) ? {\n                                    name: apiDomain.contactDetails.tech.name,\n                                    email: apiDomain.contactDetails.tech.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.tech.telnocc, \" \").concat(apiDomain.contactDetails.tech.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.tech.address1, \", \").concat(apiDomain.contactDetails.tech.city, \", \").concat(apiDomain.contactDetails.tech.country, \" \").concat(apiDomain.contactDetails.tech.zip),\n                                    company: apiDomain.contactDetails.tech.company,\n                                    contactId: apiDomain.contactDetails.tech.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                billing: ((_apiDomain_contactDetails3 = apiDomain.contactDetails) === null || _apiDomain_contactDetails3 === void 0 ? void 0 : _apiDomain_contactDetails3.billing) ? {\n                                    name: apiDomain.contactDetails.billing.name,\n                                    email: apiDomain.contactDetails.billing.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.billing.telnocc, \" \").concat(apiDomain.contactDetails.billing.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.billing.address1, \", \").concat(apiDomain.contactDetails.billing.city, \", \").concat(apiDomain.contactDetails.billing.country, \" \").concat(apiDomain.contactDetails.billing.zip),\n                                    company: apiDomain.contactDetails.billing.company,\n                                    contactId: apiDomain.contactDetails.billing.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                }\n                            },\n                            // Contact IDs for API operations\n                            contactIds: apiDomain.contacts,\n                            // Additional real data from API\n                            productCategory: apiDomain.productCategory,\n                            productKey: apiDomain.productKey,\n                            customerId: apiDomain.customerId,\n                            gdpr: apiDomain.gdpr,\n                            locks: apiDomain.locks,\n                            raaVerification: apiDomain.raaVerification,\n                            dnssec: apiDomain.dnssec,\n                            // Raw API response for debugging\n                            apiDetails: apiDomain,\n                            // Default DNS records (placeholder - would need separate API call)\n                            dnsRecords: [\n                                {\n                                    id: \"rec1\",\n                                    type: \"A\",\n                                    name: \"@\",\n                                    content: \"DNS records available via separate API\",\n                                    ttl: 14400\n                                }\n                            ]\n                        };\n                        setDomain(combinedDomain);\n                    } else {\n                        throw new Error(\"No domain data received from API\");\n                    }\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"ns1.ztech\",\n                            \"ns2.ztech\",\n                            \"ns3.ztech\",\n                            \"ns4.ztech\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            technical: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 14400\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] Toggling privacy protection for domain \".concat(domain.name, \":\"), {\n                from: domain.privacyProtection,\n                to: value,\n                orderId: domain.orderId\n            });\n            if (!domain.orderId) {\n                console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ No order ID available for domain\");\n                return;\n            }\n            // Call the modify privacy protection API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].modifyPrivacyProtection({\n                orderId: domain.orderId,\n                protectPrivacy: value,\n                reason: \"User \".concat(value ? \"enabled\" : \"disabled\", \" privacy protection via domain management panel\")\n            });\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] ✅ Privacy protection updated successfully:\", response.data);\n            // Update local state\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n            // Show success message\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Privacy protection \".concat(value ? \"enabled\" : \"disabled\", \" successfully\"));\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ Error toggling privacy protection:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to update privacy protection. Please try again.\");\n        }\n    };\n    const handlePurchasePrivacy = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDED2 [PRIVACY] Purchasing privacy protection for domain \".concat(domain.name, \":\"), {\n                orderId: domain.orderId\n            });\n            if (!domain.orderId) {\n                console.error(\"\\uD83D\\uDED2 [PRIVACY] ❌ No order ID available for domain\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Unable to purchase privacy protection. Order ID not found.\");\n                return;\n            }\n            // Call the purchase privacy protection API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].purchasePrivacyProtection({\n                orderId: domain.orderId,\n                invoiceOption: \"NoInvoice\",\n                discountAmount: 0\n            });\n            console.log(\"\\uD83D\\uDED2 [PRIVACY] ✅ Privacy protection purchased successfully:\", response.data);\n            // Update local state - privacy protection is now purchased and enabled\n            setDomain({\n                ...domain,\n                privacyProtection: true,\n                privacyProtectionDetails: {\n                    ...domain.privacyProtectionDetails,\n                    purchased: true,\n                    enabled: true\n                }\n            });\n            // Show success message\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Privacy protection purchased and enabled successfully!\");\n        } catch (error) {\n            console.error(\"\\uD83D\\uDED2 [PRIVACY] ❌ Error purchasing privacy protection:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to purchase privacy protection. Please try again.\");\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 391,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 406,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(((_domain_status = domain.status) === null || _domain_status === void 0 ? void 0 : _domain_status.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status1 = domain.status) === null || _domain_status1 === void 0 ? void 0 : _domain_status1.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status2 = domain.status) === null || _domain_status2 === void 0 ? void 0 : _domain_status2.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status3 = domain.status) === null || _domain_status3 === void 0 ? void 0 : _domain_status3.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(((_domain_status4 = domain.status) === null || _domain_status4 === void 0 ? void 0 : _domain_status4.toLowerCase()) || \"unknown\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outlined\",\n                                            className: \"border-purple-600 text-purple-600 hover:bg-purple-50 flex items-center gap-2\",\n                                            onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns\")),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 25\n                                                }, this),\n                                                dt(\"manage_dns_records\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Order ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 538,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                domain.orderId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(((_domain_status5 = domain.status) === null || _domain_status5 === void 0 ? void 0 : _domain_status5.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status6 = domain.status) === null || _domain_status6 === void 0 ? void 0 : _domain_status6.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status7 = domain.status) === null || _domain_status7 === void 0 ? void 0 : _domain_status7.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status8 = domain.status) === null || _domain_status8 === void 0 ? void 0 : _domain_status8.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(((_domain_status9 = domain.status) === null || _domain_status9 === void 0 ? void 0 : _domain_status9.toLowerCase()) || \"unknown\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.registrationDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.expiryDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: \"Security & Protection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-end gap-2\",\n                                                                            children: ((_domain_privacyProtectionDetails = domain.privacyProtectionDetails) === null || _domain_privacyProtectionDetails === void 0 ? void 0 : _domain_privacyProtectionDetails.purchased) !== false ? // Privacy protection is purchased - show toggle\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        checked: domain.privacyProtection,\n                                                                                        onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                                        color: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 613,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 rounded \".concat(domain.privacyProtection ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                                        children: domain.privacyProtection ? \"Enabled\" : \"Disabled\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 620,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 612,\n                                                                                columnNumber: 29\n                                                                            }, this) : // Privacy protection not purchased - show purchase option\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col items-end gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded\",\n                                                                                        children: \"Not Purchased\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 630,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        color: \"blue\",\n                                                                                        variant: \"outlined\",\n                                                                                        className: \"text-xs px-3 py-1\",\n                                                                                        onClick: ()=>handlePurchasePrivacy(),\n                                                                                        children: \"Purchase Privacy Protection\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 633,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderStatus && Array.isArray(domain.orderStatus) && domain.orderStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Domain Locks\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.orderStatus.map((lock, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded\",\n                                                                                    children: lock\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 654,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 652,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.domainStatus && Array.isArray(domain.domainStatus) && domain.domainStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registry Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 664,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.domainStatus.map((status, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                                                    children: status\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 669,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 667,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_raaVerification = domain.raaVerification) === null || _domain_raaVerification === void 0 ? void 0 : _domain_raaVerification.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"RAA Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.raaVerification.status === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerification.status === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerification.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_gdpr = domain.gdpr) === null || _domain_gdpr === void 0 ? void 0 : _domain_gdpr.enabled) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"GDPR Protection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.gdpr.enabled === \"true\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                            children: domain.gdpr.enabled === \"true\" ? \"Enabled\" : \"Disabled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                (((_domain_status10 = domain.status) === null || _domain_status10 === void 0 ? void 0 : _domain_status10.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" || domain.registrationError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registration Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-100 text-red-800 rounded\",\n                                                                                    children: \"Registration Failed\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 716,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                domain.registrationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-50 text-red-700 rounded\",\n                                                                                    children: domain.registrationError\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 720,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 715,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers && Array.isArray(domain.nameservers) && domain.nameservers.length > 0 ? domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 744,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 747,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 740,\n                                                                        columnNumber: 27\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_domain_status11 = domain.status) === null || _domain_status11 === void 0 ? void 0 : _domain_status11.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" ? \"Nameservers not available - Registration failed\" : \"No nameservers configured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts = domain.contacts) === null || _domain_contacts === void 0 ? void 0 : _domain_contacts.registrant) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 800,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 803,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 806,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 810,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts1 = domain.contacts) === null || _domain_contacts1 === void 0 ? void 0 : _domain_contacts1.admin) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 829,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 833,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.admin, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 837,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.admin, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.admin, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.admin, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 847,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts2 = domain.contacts) === null || _domain_contacts2 === void 0 ? void 0 : _domain_contacts2.technical) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 870,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.technical, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.technical, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 877,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.technical, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 880,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.technical, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 884,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 859,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts3 = domain.contacts) === null || _domain_contacts3 === void 0 ? void 0 : _domain_contacts3.billing) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 905,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 909,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.billing, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 913,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.billing, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.billing, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 919,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.billing, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 929,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 959,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 952,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 423,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 422,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});