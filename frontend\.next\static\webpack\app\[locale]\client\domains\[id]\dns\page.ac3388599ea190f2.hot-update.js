"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx":
/*!*****************************************************!*\
  !*** ./src/components/domains/ModernDnsManager.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModernDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ModernDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dnsRecords, setDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // Form states for adding records\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"@\",\n        content: \"\",\n        ttl: \"14400\"\n    });\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id, {\n                type: selectedRecordType\n            });\n            if (response.data.success) {\n                setDnsRecords(response.data.records || []);\n                setDnsServiceActive(true); // If we can get records, service is active\n            }\n        } catch (error) {\n            console.error(\"Error loading DNS records:\", error);\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    // Update DNS service active state when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain) {\n            console.log(\"\\uD83D\\uDD04 [DNS] Domain data updated:\", domain);\n            // Check if DNS service is already active\n            const isActive = domain.dnsServiceActive || domain.dnsActive || false;\n            console.log(\"\\uD83D\\uDD04 [DNS] Setting DNS service active state:\", isActive);\n            setDnsServiceActive(isActive);\n        }\n    }, [\n        domain\n    ]);\n    // Activate DNS Service\n    const activateDnsService = async ()=>{\n        console.log(\"Activating DNS service for domain:\", domain);\n        // Try to get the order ID from various possible fields\n        const orderIdToUse = (domain === null || domain === void 0 ? void 0 : domain.domainOrderId) || (domain === null || domain === void 0 ? void 0 : domain.orderid) || (domain === null || domain === void 0 ? void 0 : domain.orderId) || (domain === null || domain === void 0 ? void 0 : domain.id);\n        if (!orderIdToUse) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Domain order ID not found. Cannot activate DNS service.\");\n            console.error(\"Domain object:\", domain);\n            return;\n        }\n        console.log(\"Using order ID for DNS activation:\", orderIdToUse);\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDD04 [DNS] Calling DNS activation API with order ID:\", orderIdToUse);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderIdToUse);\n            console.log(\"✅ [DNS] DNS activation response received:\", response.data);\n            console.log(\"✅ [DNS] Response success flag:\", response.data.success);\n            console.log(\"✅ [DNS] Response activated flag:\", response.data.activated);\n            console.log(\"✅ [DNS] Raw API response:\", response.data.rawResponse);\n            if (response.data.success && response.data.activated) {\n                console.log(\"✅ [DNS] DNS service activated successfully, updating UI state\");\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.message || \"DNS service activated successfully!\");\n                // Reload DNS records after activation\n                console.log(\"\\uD83D\\uDD04 [DNS] Reloading DNS records after activation\");\n                await loadDnsRecords();\n                // Force a re-render by updating the domain state\n                if (typeof onUpdate === \"function\") {\n                    console.log(\"\\uD83D\\uDD04 [DNS] Updating domain state via onUpdate callback\");\n                    onUpdate({\n                        dnsServiceActive: true\n                    });\n                }\n            } else {\n                console.error(\"❌ [DNS] DNS activation failed:\", response.data);\n                throw new Error(response.data.error || response.data.message || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response_data1, _error_response2;\n            console.error(\"❌ [DNS] Error activating DNS service:\", error);\n            console.error(\"❌ [DNS] Error response:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            const errorMessage = ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data1 = _error_response2.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message || \"Failed to activate DNS service\";\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service: \" + errorMessage);\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS Record\n    const addDnsRecord = async ()=>{\n        if (!formData.content.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter the record content\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const recordData = {\n                type: selectedRecordType,\n                name: formData.name,\n                content: formData.content,\n                ttl: parseInt(formData.ttl)\n            };\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(selectedRecordType, \" record added successfully!\"));\n                setFormData({\n                    name: \"@\",\n                    content: \"\",\n                    ttl: \"3600\"\n                });\n                setShowAddForm(false);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record: \" + (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.details) || error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Copy to clipboard\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Copied to clipboard!\");\n    };\n    // Get record type icon and color\n    const getRecordTypeInfo = (type)=>{\n        const info = {\n            A: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-700\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-700\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-700\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-700\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-700\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-700\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-700\"\n            }\n        };\n        return info[type] || info.A;\n    };\n    // Format record name for display\n    const formatRecordName = (name)=>{\n        if (name === \"@\" || name === \"\") {\n            return \"@ (\".concat(domain === null || domain === void 0 ? void 0 : domain.name, \")\");\n        }\n        return \"\".concat(name, \".\").concat(domain === null || domain === void 0 ? void 0 : domain.name);\n    };\n    // Available record types (only show implemented ones)\n    const availableRecordTypes = [\n        {\n            value: \"A\",\n            label: \"A Record\",\n            description: \"IPv4 Address\",\n            implemented: true\n        },\n        {\n            value: \"AAAA\",\n            label: \"AAAA Record\",\n            description: \"IPv6 Address\",\n            implemented: true\n        },\n        {\n            value: \"CNAME\",\n            label: \"CNAME Record\",\n            description: \"Domain Alias\",\n            implemented: false\n        },\n        {\n            value: \"MX\",\n            label: \"MX Record\",\n            description: \"Mail Server\",\n            implemented: false\n        },\n        {\n            value: \"TXT\",\n            label: \"TXT Record\",\n            description: \"Text Data\",\n            implemented: false\n        },\n        {\n            value: \"NS\",\n            label: \"NS Record\",\n            description: \"Name Server\",\n            implemented: false\n        },\n        {\n            value: \"SRV\",\n            label: \"SRV Record\",\n            description: \"Service Location\",\n            implemented: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 border-l-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full \".concat(dnsServiceActive ? \"bg-green-100\" : \"bg-gray-100\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 \".concat(dnsServiceActive ? \"text-green-600\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Service Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                        value: dnsServiceActive ? \"Active\" : \"Inactive\",\n                                                        color: dnsServiceActive ? \"green\" : \"gray\",\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: dnsServiceActive ? \"DNS service is active and ready to manage records\" : \"DNS service needs to be activated before managing records\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                onClick: activateDnsService,\n                                disabled: activatingService,\n                                children: [\n                                    activatingService ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Activate DNS Service\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Manage your domain's DNS records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                        onClick: ()=>setShowAddForm(!showAddForm),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add Record\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 358,\n                                columnNumber: 15\n                            }, this),\n                            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-6 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"text-gray-800 mb-4\",\n                                        children: \"Add New DNS Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"Record Type\",\n                                                value: selectedRecordType,\n                                                onChange: (val)=>setSelectedRecordType(val),\n                                                children: availableRecordTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: type.value,\n                                                        disabled: !type.implemented,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: type.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                !type.implemented && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                    value: \"Soon\",\n                                                                    size: \"sm\",\n                                                                    color: \"amber\",\n                                                                    className: \"text-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: \"Name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                placeholder: \"@, www, mail, etc.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: selectedRecordType === \"A\" ? \"IPv4 Address\" : \"IPv6 Address\",\n                                                value: formData.content,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            content: e.target.value\n                                                        })),\n                                                placeholder: selectedRecordType === \"A\" ? \"***********\" : \"2001:db8::1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"TTL\",\n                                                value: formData.ttl,\n                                                onChange: (val)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            ttl: val\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"300\",\n                                                        children: \"5 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"1800\",\n                                                        children: \"30 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"3600\",\n                                                        children: \"1 hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"14400\",\n                                                        children: \"4 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"86400\",\n                                                        children: \"1 day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-700 flex items-center gap-2\",\n                                                onClick: addDnsRecord,\n                                                disabled: loading,\n                                                children: [\n                                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Add Record\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outlined\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 378,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: loading && dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: \"Loading DNS records...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h6\",\n                                            className: \"text-gray-600 mb-2\",\n                                            children: \"No DNS Records Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: \"Start by adding your first DNS record to configure your domain.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            onClick: ()=>setShowAddForm(true),\n                                            children: \"Add Your First Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.map((record)=>{\n                                    const typeInfo = getRecordTypeInfo(record.type);\n                                    const IconComponent = typeInfo.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg \".concat(typeInfo.bgColor),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-5 w-5 \".concat(typeInfo.textColor)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                                value: record.type,\n                                                                                color: typeInfo.color,\n                                                                                className: \"text-xs\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: formatRecordName(record.name)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 534,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"TTL: \",\n                                                                                    record.ttl,\n                                                                                    \"s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-mono text-sm text-gray-700\",\n                                                                                children: record.content\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 543,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                                content: \"Copy to clipboard\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                                    variant: \"text\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>copyToClipboard(record.content),\n                                                                                    className: \"p-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                        lineNumber: 555,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 546,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-blue-600 hover:bg-blue-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-red-600 hover:bg-red-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, record.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 357,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 356,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"blue\",\n                className: \"py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"font-medium mb-1\",\n                                children: \"DNS Management Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Currently supporting A and AAAA record types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Additional record types (CNAME, MX, TXT, NS, SRV) coming soon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• DNS changes may take 24-48 hours to propagate globally\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Always backup your DNS configuration before making changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernDnsManager, \"t1tnPQruhzp95p331Krk30rHPN0=\");\n_c = ModernDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ModernDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx\n"));

/***/ })

});