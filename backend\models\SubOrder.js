const mongoose = require('mongoose');
const OrderStatus = require('../constants/enums/order-status');
const Schema = mongoose.Schema;

// Create a new enum for SSL certificate status
const SSLCertificateStatus = {
    PENDING: 'PENDING',
    PROCESSING: 'PROCESSING',
    PENDING_VALIDATION: 'PENDING_VALIDATION',
    VALIDATED: 'VALIDATED',
    ISSUED: 'ISSUED',
    INSTALLED: 'INSTALLED',
    EXPIRED: 'EXPIRED',
    REVOKED: 'REVOKED',
    FAILED: 'FAILED'
};

// Schema for individual SSL certificates within a suborder
const sslCertificateSchema = new Schema({
    csr: {
        type: String,
        default: null
    },
    domain: {
        type: String,
        default: null
    },
    validationEmail: {
        type: String,
        default: null
    },
    status: {
        type: String,
        enum: Object.values(SSLCertificateStatus),
        default: SSLCertificateStatus.PENDING
    },
    certificateFile: {
        type: String,
        default: null
    },
    issuedAt: {
        type: Date,
        default: null
    },
    expiresAt: {
        type: Date,
        default: null
    },
    // Add the installation service field
    installService: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
});

const subOrderSchema = new Schema({
    identifiant: { type: String, required: true, unique: true },
    package: { type: Schema.Types.ObjectId, ref: 'Package', required: true },
    quantity: { type: Number, required: true },
    period: { type: Number, required: true, default: 1 },
    price: { type: Number, required: true },
    discount: { type: Number }, // Amount discounted from the price
    basedPrice: { type: Number, required: true }, // Price without discount
    status: {
        type: String,
        required: true,
        enum: [
            OrderStatus.PENDING,
            OrderStatus.PROCESSING,
            OrderStatus.COMPLETED,
            OrderStatus.SHIPPED,
            OrderStatus.PROCESSINGREFUND,
            OrderStatus.REFUNDED,
            OrderStatus.FAILED,
            OrderStatus.CANCELLED,
            OrderStatus.ACTIVE,
            OrderStatus.EXPIRED,
        ],
        default: OrderStatus.PENDING,
    },
    // New field for multiple SSL certificates
    ssl: {
        type: Map,
        of: sslCertificateSchema,
        default: new Map()
    },
    // Domain-specific information (only populated for domain-related suborders)
    domainInfo: {
        type: {
            // Domain order ID from the registrar
            domainOrderId: {
                type: String,
                required: false,
                index: true, // Index for efficient querying
            },
            // DNS Management fields
            dns: {
                activated: {
                    type: Boolean,
                    default: false
                },
                activatedAt: {
                    type: Date,
                    default: null
                },
                zoneId: {
                    type: String,
                    default: null
                }
            },
            // Privacy Protection fields
            privacy: {
                enabled: {
                    type: Boolean,
                    default: false
                },
                enabledAt: {
                    type: Date,
                    default: null
                }
            },
            // Nameserver information
            nameservers: [{
                type: String
            }],
            // Domain registration details
            registrationDetails: {
                registrar: {
                    type: String,
                    default: null
                },
                registrationDate: {
                    type: Date,
                    default: null
                },
                expiryDate: {
                    type: Date,
                    default: null
                },
                autoRenew: {
                    type: Boolean,
                    default: false
                }
            }
        },
        default: null // Will be null for non-domain suborders
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Export the SSL Certificate Status enum
module.exports.SSLCertificateStatus = SSLCertificateStatus;

const SubOrder = mongoose.model('SubOrder', subOrderSchema);
module.exports = SubOrder;
