"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [editingRecord, setEditingRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                setDnsServiceActive(true);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load DNS records. DNS service may not be activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                setShowAddForm(false);\n                setSelectedRecordType(\"A\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateDnsRecord(domain.id, editingRecord.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                setEditingRecord(null);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async (recordId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].deleteDnsRecord(domain.id, recordId);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Handle add button click\n    const handleAddClick = (recordType)=>{\n        setSelectedRecordType(recordType);\n        setShowAddForm(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            color: \"amber\",\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                \"Domain information is required to manage DNS records.\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h4\",\n                                className: \"text-gray-900\",\n                                children: \"DNS Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Manage DNS records for \",\n                                    domain.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>handleAddClick(activeTab),\n                                disabled: !dnsServiceActive,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Record\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"amber\",\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"font-medium\",\n                                        children: \"DNS Service Not Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"text-sm\",\n                                        children: \"Activate DNS service to manage DNS records for this domain.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"sm\",\n                        onClick: activateDnsService,\n                        disabled: activatingService,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 310,\n                                columnNumber: 35\n                            }, this),\n                            \"Activate DNS\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n                        value: activeTab,\n                        onChange: setActiveTab,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsHeader, {\n                                className: \"bg-gray-50 p-1 m-6 mb-0\",\n                                children: recordTypes.map((param)=>{\n                                    let { type, label } = param;\n                                    const count = getRecordsForType(type).length;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tab, {\n                                        value: type,\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, this),\n                                            count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsBody, {\n                                className: \"p-6\",\n                                children: recordTypes.map((param)=>{\n                                    let { type } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabPanel, {\n                                        value: type,\n                                        className: \"p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            records: getRecordsForType(type),\n                                            recordType: type,\n                                            onEdit: setEditingRecord,\n                                            onDelete: handleDeleteRecord,\n                                            onAdd: handleAddClick,\n                                            domain: domain,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, type, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAddForm || !!editingRecord,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: editingRecord ? handleEditRecord : handleAddRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"Umk11zo33J4nFXgzUl3biy5JVCM=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});