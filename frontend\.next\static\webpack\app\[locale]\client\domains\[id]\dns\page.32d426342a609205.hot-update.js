"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [editingRecord, setEditingRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                setDnsServiceActive(true);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load DNS records. DNS service may not be activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS service activated successfully!\");\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                setShowAddForm(false);\n                setSelectedRecordType(\"A\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateDnsRecord(domain.id, editingRecord.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                setEditingRecord(null);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async (recordId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].deleteDnsRecord(domain.id, recordId);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Handle add button click\n    const handleAddClick = (recordType)=>{\n        setSelectedRecordType(recordType);\n        setShowAddForm(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            color: \"amber\",\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                \"Domain information is required to manage DNS records.\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h4\",\n                                className: \"text-gray-900\",\n                                children: \"DNS Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Manage DNS records for \",\n                                    domain.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>handleAddClick(activeTab),\n                                disabled: !dnsServiceActive,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Record\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"amber\",\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"font-medium\",\n                                        children: \"DNS Service Not Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"text-sm\",\n                                        children: \"Activate DNS service to manage DNS records for this domain.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"sm\",\n                        onClick: activateDnsService,\n                        disabled: activatingService,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 298,\n                                columnNumber: 35\n                            }, this),\n                            \"Activate DNS\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n                        value: activeTab,\n                        onChange: setActiveTab,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsHeader, {\n                                className: \"bg-gray-50 p-1 m-6 mb-0\",\n                                children: recordTypes.map((param)=>{\n                                    let { type, label } = param;\n                                    const count = getRecordsForType(type).length;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tab, {\n                                        value: type,\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, this),\n                                            count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsBody, {\n                                className: \"p-6\",\n                                children: recordTypes.map((param)=>{\n                                    let { type } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabPanel, {\n                                        value: type,\n                                        className: \"p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            records: getRecordsForType(type),\n                                            recordType: type,\n                                            onEdit: setEditingRecord,\n                                            onDelete: handleDeleteRecord,\n                                            onAdd: handleAddClick,\n                                            domain: domain,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, type, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAddForm || !!editingRecord,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: editingRecord ? handleEditRecord : handleAddRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"m9UUzP3GoQqildbNfdPac/Odyk4=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});