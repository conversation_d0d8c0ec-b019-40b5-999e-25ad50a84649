const Order = require("../models/Order");
const SubOrder = require("../models/SubOrder");
const Cart = require("../models/Cart");
const User = require("../models/User");
const OrderStatus = require("../constants/enums/order-status");
const PaymentMethod = require("../constants/enums/payment-method");
const Category = require("../models/Category");
const Brand = require("../models/Brand");
const Package = require("../models/Package");
const ProductStatus = require("../constants/enums/poduct-status");
const { processPayment } = require("./paymentController");
const { v4: uuidv4 } = require("uuid");

// Use global logging system
const createLogger = require("../utils/globalLogger");

// Create a new order from the cart
exports.createOrder = async (req, res) => {
  const logger = createLogger("createOrder");
  logger.log("Create Order......");
  try {
    const userId = req.user?._id;
    const {
      BillToName,
      email,
      phone,
      address,
      country,
      isCompany,
      companyICE,
      companyEmail,
      companyPhone,
      companyAddress,
    } = req.body;

    // const cart = await Cart.findOne({ user: userId });
    const cart = await Cart.findOne({ user: userId }).populate("items.package");
    if (!cart || cart.items.length === 0) {
      return res.status(400).json({
        cartIsEmpty: true,
        message: req.t("order.cart_is_empty"),
      });
    }

    const shippingFee = 0;
    const tax = 0.2;
    let totalPrice = cart.totalPrice * (1 + tax);
    let taxAmount = cart.totalPrice * tax;
    let orderIdentifiant = uuidv4();

    let totalDiscount = 0;
    let subTotal = 0;
    // Create subOrders from cart items
    logger.log(
      "Cart items:",
      cart.items.map((item) => ({
        type: item.type,
        package: item.package ? item.package._id : "N/A",
        domainName: item.domainName || "N/A",
        price: item.price,
        period: item.period,
      }))
    );

    const subOrders = await Promise.all(
      cart.items.map(async (item) => {
        totalDiscount += item.discount;
        const basedPrice = item.price * item.quantity * item.period;
        subTotal = subTotal + basedPrice;
        const subOrderIdentifiant = uuidv4().split("-")[0];

        // Handle domain items differently
        if (item.type === "domain") {
          logger.log("Processing domain item:", item.domainName);
          // For domain items, we need to find or create a package for the domain
          const Package = require("../models/Package");
          const Brand = require("../models/Brand");
          const Category = require("../models/Category");
          const ProductStatus = require("../constants/enums/poduct-status");

          // Find or create the Domain category
          let domainCategory = await Category.findOne({ name: "Domains" });

          // if (!domainCategory) {
          //   domainCategory = new Category({
          //     name: "Domains",
          //     name_fr: "Domaines",
          //     description: "Domain registration services",
          //     description_fr: "Services d'enregistrement de domaines",
          //   });
          //   await domainCategory.save();
          // }

          // Find or create the Domain brand
          let domainBrand = await Brand.findOne({
            name: "Domain Registration",
          });

          // if (!domainBrand) {
          //   domainBrand = new Brand({
          //     name: "Domain Registration",
          //     name_fr: "Enregistrement de Domaine",
          //     description: "Domain registration services",
          //     description_fr: "Services d'enregistrement de domaines",
          //     category: domainCategory._id,
          //   });
          //   await domainBrand.save();
          // }

          // Create a reference for the domain package
          const domainRef = `domain-${item.domainName}`;
          logger.log("Domain reference:", domainRef);

          // Find or create a package for this domain
          let domainPackage = await Package.findOne({ reference: domainRef });
          if (!domainPackage) {
            logger.log("Creating new domain package for:", item.domainName);
            domainPackage = new Package({
              reference: domainRef,
              name: item.domainName,
              name_fr: item.domainName,
              description: `Domain registration for ${item.domainName}`,
              description_fr: `Enregistrement de domaine pour ${item.domainName}`,
              price: item.price / item.period, // Store the price per period
              category: domainCategory._id,
              brand: domainBrand._id,
              status: ProductStatus.PUBLISHED,
            });
            await domainPackage.save();
            logger.log("Domain package created with ID:", domainPackage._id);
          } else {
            logger.log(
              "Found existing domain package with ID:",
              domainPackage._id
            );
          }

          logger.log(
            "Creating SubOrder for domain with package ID:",
            domainPackage._id
          );
          const subOrder = new SubOrder({
            identifiant: subOrderIdentifiant,
            package: domainPackage._id,
            quantity: item.quantity,
            basedPrice, // Price before discount
            price: basedPrice - item.discount, // Price per item
            discount: item.discount, // Discount for this sub-order
            period: item.period,
          });

          try {
            await subOrder.save();
            logger.log(
              "Domain SubOrder created successfully with ID:",
              subOrder._id
            );
            return subOrder._id;
          } catch (error) {
            logger.error("Error creating domain SubOrder:", error);
            throw error;
          }
        } else {
          // For regular package items
          logger.log(
            "Processing regular package item:",
            item.package ? item.package._id : "undefined"
          );

          if (!item.package) {
            logger.error("Package is undefined for non-domain item:", item);
            throw new Error("Package is required for non-domain items");
          }

          const subOrder = new SubOrder({
            identifiant: subOrderIdentifiant,
            package: item.package,
            quantity: item.quantity,
            basedPrice, // Price before discount
            price: basedPrice - item.discount, // Price per item
            discount: item.discount, // Discount for this sub-order
            period: item.period,
          });

          try {
            await subOrder.save();
            logger.log(
              "Regular package SubOrder created successfully with ID:",
              subOrder._id
            );
            return subOrder._id;
          } catch (error) {
            logger.error("Error creating regular package SubOrder:", error);
            throw error;
          }
        }
      })
    );
    // totalPrice -= orderDiscount; // Apply discount to totalPrice

    // Create the order
    const newOrder = new Order({
      user: userId,
      identifiant: orderIdentifiant.split("-")[0],
      subOrders,
      paymentMethod: PaymentMethod.PAYZONE,
      status: OrderStatus.PENDING,
      taxRate: tax,
      totalPrice,
      totalDiscount,
      taxAmount,
      subTotal,
      shippingFee,
      billingInfo: {
        BillToName,
        email,
        phone,
        address,
        country,
        isCompany: isCompany || false,
        companyICE,
        companyEmail,
        companyPhone,
        companyAddress,
      },
      isPaid: false,
    });

    // Save the order to the database
    const savedOrder = await newOrder.save();
    // Fetch the populated order
    const order = await Order.findById(savedOrder._id).populate("user");

    // Debug: Log cart items before domain registration
    logger.log(
      "🔍 [DEBUG] Cart items before domain registration:",
      cart.items
        .filter((item) => item.type === "domain")
        .map((item) => ({
          domainName: item.domainName,
          privacyProtection: item.privacyProtection,
          autoRenew: item.autoRenew,
          type: item.type,
          period: item.period,
        }))
    );

    // Register domain names immediately after order creation
    await registerDomainsInOrder(order, cart.items);
    // Process initial payment record
    // COMMENTED OUT: Payment will now be processed after successful CMI callback

    const services = cart.items.map((item) => ({
      serviceId: item.package,
      name: item.package?.name || "Unknown Service", // Ensure a default name
      period: item.period,
      price: item.price * item.quantity * item.period,
      discount: item.discount,
      quantity: item.quantity,
    }));

    await processPayment({
      userId,
      orderId: order._id,
      totalPrice: order.totalPrice,
      subTotal: order.subTotal,
      totalDiscount: order.totalDiscount,
      taxAmount: order.taxAmount,
      paymentMethod: order.paymentMethod,
      status: "completed",
      billingInfo: {
        name: BillToName,
        email,
        phone,
        address,
        country,
      },
      services,
    });

    // Clear the cart after the order is created
    // await Cart.findOneAndUpdate(
    //   { user: userId },
    //   { $set: { items: [], totalPrice: 0, totalDiscount: 0, cartCount: 0 } }
    // );

    // Send notification about new order
    if (global.io) {
      setImmediate(async () => {
        try {
          const socketService = require("../services/socketService")(global.io);

          // Populate user information for the notification
          const populatedOrder = await Order.findById(savedOrder._id).populate(
            "user",
            "firstName lastName email"
          );

          await socketService.notifyNewOrder(populatedOrder);
          logger.log(
            `[SOCKET DEBUG] Notification sent for new order ${savedOrder._id}`
          );
        } catch (error) {
          logger.error("Error sending new order notification:", error);
        }
      });
    }

    res.status(201).json({
      message: req.t("order.order_creation_success"),
      order,
    });
  } catch (error) {
    logger.error("Order creation error:", error);
    if (error.name === "ValidationError") {
      logger.error("Validation error details:", error.errors);
      return res.status(400).json({
        message: "Validation error",
        details: error.message,
        errors: Object.keys(error.errors).reduce((acc, key) => {
          acc[key] = error.errors[key].message;
          return acc;
        }, {}),
      });
    }
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

exports.getMyOrders = async (req, res) => {
  const logger = createLogger("getMyOrders");
  try {
    const userId = req.user?._id;

    // Retrieve orders and populate the package field in subOrders
    const orders = await Order.find({ user: userId }).populate({
      path: "subOrders",
      populate: {
        path: "package",
        model: "Package",
      },
    });

    if (orders.length === 0) {
      return res.status(400).json({ message: req.t("order.no_orders") });
    }

    res.status(200).json({
      message: req.t("order.order_retrieved_successfully"),
      orders,
    });
  } catch (error) {
    logger.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Get a specific order
exports.getOrder = async (req, res) => {
  const logger = createLogger("getOrder");
  try {
    const { orderId } = req.params;
    logger.log("getting order: ", orderId);
    const order = await Order.findOne({
      _id: orderId,
      status: { $ne: OrderStatus.DELETED },
    })
      .populate("user")
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
        },
      });

    if (!order) {
      return res.status(404).json({ message: req.t("order.order_not_found") });
    }
    res.status(200).json({
      order,
      message: req.t("order_retrieved_successfully"),
    });
  } catch (error) {
    logger.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

exports.getSubOrdersByUserIdAndCategory = async (req, res) => {
  const logger = createLogger("getSubOrdersByUserIdAndCategory");
  try {
    const userId = req.user?._id;
    const { categoryName } = req.params;
    logger.log("getting categoryName____: ", categoryName);

    // Find the category by name
    const category = await Category.findOne({ name: categoryName });
    if (!category) {
      return res.status(404).json({ message: "Category not found" });
    }

    // Retrieve orders by userId and populate subOrders and package
    const orders = await Order.find({
      user: userId,
      status: {
        $nin: [OrderStatus.DELETED, OrderStatus.CANCELLED, OrderStatus.PENDING],
      },
    })
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
          populate: {
            path: "brand",
            model: "Brand",
            select: "category name name_fr",
            match: { category: category._id },
          },
        },
      })
      .sort({ createdAt: -1 })
      .lean(); // Use lean to improve performance

    // Filter subOrders by category
    const subOrders = orders.reduce((acc, order) => {
      const filteredSubOrders = order.subOrders.filter(
        (subOrder) => subOrder.package && subOrder.package.brand
      );
      return acc.concat(filteredSubOrders);
    }, []);

    if (subOrders.length === 0) {
      return res.status(400).json({ message: "No suborders found" });
    }

    res.status(200).json({
      message: "Suborders retrieved successfully",
      subOrders,
    });
  } catch (error) {
    logger.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Get user's domain orders
exports.getUserDomainOrders = async (req, res) => {
  const logger = createLogger("getUserDomainOrders");
  try {
    const userId = req.user?._id;

    // Find the Domains category
    const category = await Category.findOne({ name: "Domains" });
    if (!category) {
      return res.status(404).json({ message: "Domains category not found" });
    }

    // Retrieve orders by userId and populate subOrders and package
    const orders = await Order.find({
      user: userId,
      status: {
        $nin: [OrderStatus.DELETED, OrderStatus.CANCELLED],
      },
    })
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
          populate: {
            path: "brand",
            model: "Brand",
            select: "category name name_fr",
            match: { category: category._id },
          },
        },
      })
      .sort({ createdAt: -1 })
      .lean(); // Use lean to improve performance

    // Filter subOrders by domain category and transform to domain format
    const domainOrders = [];

    orders.forEach((order) => {
      const filteredSubOrders = order.subOrders.filter(
        (subOrder) => subOrder.package && subOrder.package.brand
      );

      filteredSubOrders.forEach((subOrder) => {
        // Format domain data
        domainOrders.push({
          id: subOrder._id,
          name: subOrder.package.name, // Domain name is stored in the package name
          status:
            subOrder.status === OrderStatus.FAILED
              ? "failed"
              : subOrder.status === OrderStatus.ACTIVE
              ? "active"
              : subOrder.status === OrderStatus.PENDING
              ? "pending"
              : subOrder.status?.toLowerCase() || "unknown",
          orderStatus: subOrder.status, // Include the actual suborder status
          registrationDate: order.createdAt,
          expiryDate: new Date(
            new Date(order.createdAt).setFullYear(
              new Date(order.createdAt).getFullYear() + subOrder.period
            )
          ),
          autoRenew: false, // Default to false (auto-renewal disabled)
          registrar: "ZTech Domains",
          period: subOrder.period,
          price: subOrder.price,
          orderId: order._id,
          orderid: subOrder.domainInfo?.domainOrderId || subOrder.domainOrderId, // Support both new and legacy structure
          // DNS Management fields (from new nested structure)
          dnsActivated: subOrder.domainInfo?.dns?.activated || subOrder.dnsActivated || false,
          dnsActivatedAt: subOrder.domainInfo?.dns?.activatedAt || subOrder.dnsActivatedAt || null,
          dnsZoneId: subOrder.domainInfo?.dns?.zoneId || subOrder.dnsZoneId || null,
          // Privacy Protection fields
          privacyEnabled: subOrder.domainInfo?.privacy?.enabled || false,
          privacyEnabledAt: subOrder.domainInfo?.privacy?.enabledAt || null,
          // Domain registration details
          domainRegistrationDetails: subOrder.domainInfo?.registrationDetails || null,
          // Add registration error information for failed domains
          registrationError:
            subOrder.status === OrderStatus.FAILED
              ? "Registry error - Domain registration failed"
              : null,
          nameservers:
            subOrder.status === OrderStatus.FAILED
              ? [] // No nameservers for failed registrations
              : ["ns1.ztech", "ns2.ztech", "ns3.ztech", "ns4.ztech"], // Default nameservers for successful registrations
          privacyProtection: true, // Default to true
        });
      });
    });

    res.status(200).json({
      message: "Domain orders retrieved successfully",
      domains: domainOrders,
    });
  } catch (error) {
    logger.error("Error retrieving domain orders:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// Update the order status (e.g., for processing, completed, etc.)
exports.updateOrderStatus = async (req, res) => {
  const logger = createLogger("updateOrderStatus");
  const { status } = req.body;
  const { orderId } = req.params;
  try {
    // Find the order and populate user information
    const order = await Order.findById(orderId).populate(
      "user",
      "firstName lastName email"
    );
    if (!order) {
      return res.status(404).json({ message: req.t("order_not_found") });
    }

    // Validate the new status
    const validStatuses = [
      OrderStatus.PENDING,
      OrderStatus.COMPLETED,
      OrderStatus.CANCELLED,
      OrderStatus.PROCESSING,
      OrderStatus.DELETED,
      OrderStatus.FAILED,
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: "Invalid status" });
    }

    // Store previous status for notification
    const previousStatus = order.status;

    // Update the order status
    order.status = status;
    const savedOrder = await order.save();

    // Send notification about status update if status has changed
    if (global.io && previousStatus !== status) {
      setImmediate(async () => {
        try {
          const socketService = require("../services/socketService")(global.io);
          const user = await User.findById(req.user?._id).select(
            "firstName lastName role"
          );
          await socketService.notifyOrderStatusUpdate(
            savedOrder,
            previousStatus,
            user
          );
          logger.log(
            `[SOCKET DEBUG] Notification sent for order status update ${savedOrder._id}`
          );
        } catch (error) {
          logger.error("Error sending order status notification:", error);
        }
      });
    }

    res.status(200).json({
      order: savedOrder,
      message: "Order status updated successfully",
    });
  } catch (error) {
    logger.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Mark the order as paid
exports.markOrderAsPaid = async (req, res) => {
  const logger = createLogger("markOrderAsPaid");
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId).populate(
      "user",
      "firstName lastName email"
    );
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    // Store previous payment status
    const previouslyPaid = order.isPaid;

    // Update payment status
    order.isPaid = true;
    order.datePaid = Date.now();

    // Update payment record
    const payment = await processPayment({
      userId: order.user,
      orderId: order._id,
      totalPrice: order.totalPrice,
      paymentMethod: order.paymentMethod,
      status: "completed",
      transactionId: order.transactionId,
      billingInfo: order.billingInfo,
    });

    const updatedOrder = await order.save();

    // Send notification about payment confirmation if this is a new payment
    if (global.io && !previouslyPaid) {
      setImmediate(async () => {
        try {
          const socketService = require("../services/socketService")(global.io);
          await socketService.notifyPaymentConfirmation(updatedOrder, payment);
          logger.log(
            `[SOCKET DEBUG] Payment confirmation notification sent for order ${updatedOrder._id}`
          );
        } catch (error) {
          logger.error(
            "Error sending payment confirmation notification:",
            error
          );
        }
      });
    }

    res.status(200).json({
      order: updatedOrder,
      message: "Order marked as paid successfully",
    });
  } catch (error) {
    logger.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Refund the order
exports.refundOrder = async (req, res) => {
  const logger = createLogger("refundOrder");
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    order.status = OrderStatus.PROCESSINGREFUND; // Set the status to refunding
    await order.save();

    res.status(200).json({
      order,
      message: "Order refund requested successfully",
    });
  } catch (error) {
    logger.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Add this function to the exports

// Get a specific suborder by ID
exports.getSubOrderById = async (req, res) => {
  const logger = createLogger("getSubOrderById");
  try {
    const { subOrderId } = req.params;
    const userId = req.user?._id;

    const subOrder = await SubOrder.findById(subOrderId).populate({
      path: "package",
      populate: {
        path: "brand",
        model: "Brand",
      },
    });

    if (!subOrder) {
      return res.status(404).json({ message: req.t("order.order_not_found") });
    }

    // Find the parent order to verify ownership
    const parentOrder = await Order.findOne({
      subOrders: subOrderId,
      user: userId,
    });

    if (!parentOrder) {
      return res.status(403).json({ message: req.t("errors.not_authorized") });
    }

    // Add the parent order reference to the subOrder
    const result = subOrder.toObject();
    result.parentOrder = parentOrder;

    res.status(200).json(result);
  } catch (error) {
    logger.error("Error fetching suborder:", error);
    res.status(500).json({ message: req.t("errors.server_error") });
  }
};

// Function to register domains in an order 
async function registerDomainsInOrder(order, cartItems) {
  const logger = createLogger("registerDomainsInOrder");
  try {
    logger.log(
      "🌐 [DOMAIN_REG] Starting domain registration for order:",
      order._id
    );
    logger.log("🌐 [DOMAIN_REG] Order user ID:", order.user?._id || order.user);
    logger.log("🌐 [DOMAIN_REG] Cart items received:", cartItems?.length || 0);

    // Log all cart items for debugging
    cartItems.forEach((item, index) => {
      logger.log(`🌐 [DOMAIN_REG] Cart item ${index}:`, {
        type: item.type,
        domainName: item.domainName,
        package: item.package?._id || item.package,
        price: item.price,
        period: item.period,
      });
    });

    // Filter cart items to get only domain items
    const domainItems = cartItems.filter((item) => item.type === "domain");

    if (domainItems.length === 0) {
      logger.log("🌐 [DOMAIN_REG] ❌ No domain items found in order");
      return;
    }

    logger.log(
      `🌐 [DOMAIN_REG] ✅ Found ${domainItems.length} domain(s) to register:`,
      domainItems.map((item) => item.domainName)
    );

    // Import required modules (moved to top of file)
    const axios = require("axios");

    // API configuration
    const API_BASE_URL = process.env.API_BASE_URL_TEST;
    const AUTH_PARAMS = {
      "auth-userid": process.env.AUTH_USERID_TEST,
      "api-key": process.env.API_KEY_TEST,
    };
    const COMPANY_CUSTOMER_ID = process.env.COMPANY_CUSTOMER_ID || "31174676";

    logger.log("🌐 [DOMAIN_REG] API Configuration:", {
      API_BASE_URL,
      AUTH_USERID: AUTH_PARAMS["auth-userid"],
      API_KEY_EXISTS: !!AUTH_PARAMS["api-key"],
      COMPANY_CUSTOMER_ID,
    });

    // Get default nameservers from API
    let DEFAULT_NAMESERVERS = []; // Will be populated from API

    try {
      // Get customer default nameservers from API
      logger.log(
        "🌐 [DOMAIN_REG] 🔍 Fetching customer default nameservers for domain registration..."
      );
      const nsResponse = await axios.get(
        `${API_BASE_URL}/domains/customer-default-ns.json`,
        {
          params: {
            ...AUTH_PARAMS,
            "customer-id": COMPANY_CUSTOMER_ID,
          },
          timeout: 10000,
        }
      );

      logger.log(
        "🌐 [DOMAIN_REG] 🔍 API nameserver response:",
        nsResponse.data
      );

      if (
        nsResponse.data &&
        Array.isArray(nsResponse.data) &&
        nsResponse.data.length > 0
      ) {
        DEFAULT_NAMESERVERS = nsResponse.data;
        logger.log(
          "🌐 [DOMAIN_REG] ✅ Using customer default nameservers for registration:",
          DEFAULT_NAMESERVERS
        );
      } else {
        logger.log(
          "🌐 [DOMAIN_REG] ⚠️ API returned empty nameservers, using fallback nameservers"
        );
        // Use minimal fallback nameservers
        DEFAULT_NAMESERVERS = ["ns1.example.com", "ns2.example.com"];
      }
    } catch (nsError) {
      logger.warn(
        "🌐 [DOMAIN_REG] ⚠️ Could not fetch default nameservers from API, using fallback nameservers:",
        nsError.message
      );
      // Use minimal fallback nameservers
      DEFAULT_NAMESERVERS = ["ns1.example.com", "ns2.example.com"];
    }

    // Get user's domain contacts
    logger.log(
      "🌐 [DOMAIN_REG] 👤 Fetching user domain contacts for user:",
      order.user._id
    );
    const user = await User.findById(order.user._id).populate(
      "domainContacts.registrant domainContacts.admin domainContacts.tech domainContacts.billing"
    );

    if (!user) {
      logger.error("🌐 [DOMAIN_REG] ❌ User not found for domain registration");
      return;
    }

    logger.log("🌐 [DOMAIN_REG] 👤 User found:", {
      id: user._id,
      email: user.email,
      hasDomainContacts: !!user.domainContacts,
    });

    // Check if user has domain contacts
    const userContacts = {
      registrant: user.domainContacts?.registrant?.externalContactId,
      admin: user.domainContacts?.admin?.externalContactId,
      tech: user.domainContacts?.tech?.externalContactId,
      billing: user.domainContacts?.billing?.externalContactId,
    };

    logger.log("🌐 [DOMAIN_REG] 👤 User contacts:", userContacts);

    // If user doesn't have contacts, we'll need to create default ones or skip registration
    if (
      !userContacts.registrant ||
      !userContacts.admin ||
      !userContacts.tech ||
      !userContacts.billing
    ) {
      logger.warn(
        "🌐 [DOMAIN_REG] ⚠️ User doesn't have all required domain contacts. Skipping domain registration."
      );
      logger.warn(
        "🌐 [DOMAIN_REG] Required contacts: registrant, admin, tech, billing"
      );
      logger.warn("🌐 [DOMAIN_REG] Missing contacts:", {
        registrant: !userContacts.registrant,
        admin: !userContacts.admin,
        tech: !userContacts.tech,
        billing: !userContacts.billing,
      });
      return;
    }

    // Register each domain
    logger.log("🌐 [DOMAIN_REG] 🔄 Starting domain registration loop...");
    for (const domainItem of domainItems) {
      try {
        logger.log(
          `🌐 [DOMAIN_REG] 🔄 Processing domain: ${domainItem.domainName}`
        );

        // Debug: Log the complete domainItem object
        logger.log(
          `🔍 [DEBUG] Complete domainItem object for ${domainItem.domainName}:`,
          {
            domainName: domainItem.domainName,
            type: domainItem.type,
            period: domainItem.period,
            price: domainItem.price,
            privacyProtection: domainItem.privacyProtection,
            autoRenew: domainItem.autoRenew,
            allFields: Object.keys(domainItem),
          }
        );

        // Find the corresponding suborder for this domain
        logger.log(
          "🌐 [DOMAIN_REG] 🔍 Finding suborders for order:",
          order._id
        );
        const subOrders = await SubOrder.find({
          _id: { $in: order.subOrders },
        }).populate("package");

        logger.log("🌐 [DOMAIN_REG] 📋 Found suborders:", subOrders.length);
        subOrders.forEach((sub, index) => {
          logger.log(`🌐 [DOMAIN_REG] SubOrder ${index}:`, {
            id: sub._id,
            packageName: sub.package?.name,
            packageReference: sub.package?.reference,
            type: sub.type,
          });
        });

        // For domain items, find suborder by matching domain name
        // Domain items might not have a package, so we check the item details
        const subOrder = subOrders.find((sub) => {
          // Check if this is a domain suborder
          if (sub.package && sub.package.name === domainItem.domainName) {
            return true;
          }
          // For domain items without package, we might need to match differently
          // This depends on how domain items are stored in suborders
          return false;
        });

        if (!subOrder) {
          logger.error(
            `🌐 [DOMAIN_REG] ❌ SubOrder not found for domain: ${domainItem.domainName}`
          );
          logger.log(
            "🌐 [DOMAIN_REG] Available suborders:",
            subOrders.map((s) => ({
              id: s._id,
              packageName: s.package?.name,
              packageReference: s.package?.reference,
              type: s.type,
            }))
          );
          continue;
        }

        logger.log(
          `🌐 [DOMAIN_REG] ✅ Found matching suborder for ${domainItem.domainName}:`,
          subOrder._id
        );

        // Prepare registration parameters
        const params = {
          ...AUTH_PARAMS,
          "domain-name": domainItem.domainName,
          years: domainItem.period || 1,
          "customer-id": COMPANY_CUSTOMER_ID,
          "reg-contact-id": userContacts.registrant,
          "admin-contact-id": userContacts.admin,
          "tech-contact-id": userContacts.tech,
          "billing-contact-id": userContacts.billing,
          "invoice-option": "NoInvoice",
          "auto-renew": domainItem.autoRenew || false,
        };

        // Add privacy protection if requested by user
        logger.log(
          `🔒 [PRIVACY] Domain ${domainItem.domainName} privacy setting:`,
          {
            privacyProtection: domainItem.privacyProtection,
            willPurchasePrivacy: !!domainItem.privacyProtection,
            willProtectPrivacy: !!domainItem.privacyProtection,
          }
        );

        if (domainItem.privacyProtection) {
          params["purchase-privacy"] = true;
          params["protect-privacy"] = true;
          logger.log(
            `🔒 [PRIVACY] ✅ Privacy protection ENABLED for ${domainItem.domainName}`
          );
        } else {
          logger.log(
            `🔒 [PRIVACY] ❌ Privacy protection DISABLED for ${domainItem.domainName}`
          );
        }

        logger.log("🔧 Domain registration parameters:", {
          domain: domainItem.domainName,
          customerId: COMPANY_CUSTOMER_ID,
          contacts: userContacts,
          nameservers: DEFAULT_NAMESERVERS,
        });

        // Create URLSearchParams for proper nameserver handling
        const searchParams = new URLSearchParams();

        // Add all parameters except nameservers
        for (const [key, value] of Object.entries(params)) {
          searchParams.append(key, value);
        }

        // Add each nameserver as a separate 'ns' parameter
        DEFAULT_NAMESERVERS.forEach((nameserver) => {
          searchParams.append("ns", nameserver);
        });

        logger.log(
          "🌐 [DOMAIN_REG] 🌐 Final API URL parameters:",
          searchParams.toString()
        );

        // Validate required parameters before API call
        logger.log(`🌐 [DOMAIN_REG] 🔍 Validating registration parameters...`);

        const requiredParams = [
          "auth-userid",
          "api-key",
          "domain-name",
          "customer-id",
          "reg-contact-id",
          "admin-contact-id",
          "tech-contact-id",
          "billing-contact-id",
        ];
        const missingParams = requiredParams.filter(
          (param) => !searchParams.has(param)
        );

        if (missingParams.length > 0) {
          logger.error(
            `🌐 [DOMAIN_REG] ❌ Missing required parameters: ${missingParams.join(
              ", "
            )}`
          );
          continue;
        }

        logger.log(`🌐 [DOMAIN_REG] ✅ All required parameters present`);

        // Call the domain registration API
        // Check if we're using test/demo environment
        const isTestEnvironment =
          API_BASE_URL.includes("test") || API_BASE_URL.includes("demo");
        logger.log(
          `🌐 [DOMAIN_REG] 🧪 Test Environment Detected: ${isTestEnvironment}`
        );

        logger.log(
          `🌐 [DOMAIN_REG] 🚀 Calling domain registration API for ${domainItem.domainName}...`
        );
        const response = await axios.post(
          `${API_BASE_URL}/domains/register.json?${searchParams.toString()}`,
          null,
          {
            timeout: 30000, // Increased timeout for domain registration
          }
        );

        // Log the complete API response for debugging
        logger.log(
          `🌐 [DOMAIN_REG] 📋 Complete API Response:`,
          JSON.stringify(response.data, null, 2)
        );

        // Check if domain registration was actually successful
        const registrationStatus =
          response.data?.actionstatus || response.data?.status;

        logger.log(
          `🌐 [DOMAIN_REG] 🔍 Checking registration status: "${registrationStatus}"`
        );

        // For demo/test servers, we might need to check different success indicators
        let isSuccess = false;

        if (isTestEnvironment) {
          // In test environment, check for different success patterns
          // BUT exclude registry errors even if they have entityid/eaqid
          const hasRegistryError = response.data?.actionstatusdesc
            ?.toLowerCase()
            .includes("registry error");

          isSuccess =
            (registrationStatus === "Success" ||
              registrationStatus === "success" ||
              registrationStatus === "Successful" ||
              registrationStatus === "successful" ||
              // Some test servers return entityid even for "Failed" status but it's actually successful
              (response.data?.entityid && response.data?.eaqid) ||
              // Some test APIs return specific test success indicators
              response.data?.actiontypedesc?.includes("Registration")) &&
            // EXCLUDE registry errors - these are real failures
            !hasRegistryError;

          logger.log(
            `🌐 [DOMAIN_REG] 🧪 Test Environment Success Check: ${isSuccess} (entityid: ${response.data?.entityid}, eaqid: ${response.data?.eaqid}, hasRegistryError: ${hasRegistryError})`
          );
        } else {
          // Production environment - strict success checking
          isSuccess =
            registrationStatus === "Success" ||
            registrationStatus === "success" ||
            registrationStatus === "Successful" ||
            registrationStatus === "successful";
        }

        if (isSuccess) {
          logger.log(
            `🌐 [DOMAIN_REG] ✅ Domain registration successful for ${domainItem.domainName}:`,
            response.data
          );

          // Extract order ID from the API response
          const orderId = response.data?.entityid || response.data?.order_id;

          if (isSuccess && orderId) {
            logger.log(
              `🌐 [DOMAIN_REG] ✅ Storing order ID (${orderId}) in suborder: ${subOrder._id}`
            );

            // Update suborder with domainOrderId and status
            await SubOrder.findByIdAndUpdate(subOrder._id, {
              status: OrderStatus.ACTIVE,
              domainOrderId: orderId, // Store the order ID as domainOrderId in the database
            });

            logger.log(
              `🌐 [DOMAIN_REG] ✅ Suborder ${subOrder._id} updated with domainOrderId: ${orderId}`
            );
          }
        } else {
          // Registration failed
          logger.error(
            `🌐 [DOMAIN_REG] ❌ Domain registration FAILED for ${domainItem.domainName}:`,
            response.data
          );

          logger.error(
            `🌐 [DOMAIN_REG] ❌ Registration status: ${registrationStatus}`
          );

          // Log additional error details if available
          if (response.data?.actionstatusdesc) {
            logger.error(
              `🌐 [DOMAIN_REG] ❌ Error description: ${response.data.actionstatusdesc}`
            );
          }

          if (response.data?.message) {
            logger.error(
              `🌐 [DOMAIN_REG] ❌ Error message: ${response.data.message}`
            );
          }

          if (response.data?.error) {
            logger.error(
              `🌐 [DOMAIN_REG] ❌ Error details: ${JSON.stringify(
                response.data.error
              )}`
            );
          }

          // Update suborder status to FAILED
          logger.log(
            `🌐 [DOMAIN_REG] 📝 Updating suborder ${subOrder._id} status to FAILED...`
          );
          await SubOrder.findByIdAndUpdate(subOrder._id, {
            status: OrderStatus.FAILED,
          });

          logger.log(
            `🌐 [DOMAIN_REG] ❌ Updated suborder ${subOrder._id} status to FAILED`
          );

          // Continue with other domains even if this one failed
          continue;
        }
      } catch (domainError) {
        logger.error(
          `🌐 [DOMAIN_REG] ❌ Domain registration error for ${domainItem.domainName}:`,
          domainError.response?.data || domainError.message
        );
        logger.error(`🌐 [DOMAIN_REG] ❌ Full error details:`, domainError);

        // Continue with other domains even if one fails
        continue;
      }
    }

    logger.log(
      "🌐 [DOMAIN_REG] 🎉 Domain registration process completed for order:",
      order._id
    );
  } catch (error) {
    logger.error("🌐 [DOMAIN_REG] ❌ Error in registerDomainsInOrder:", error);
  }
}
