"use client";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@material-tailwind/react";
import {
  Edit,
  Trash2,
  Globe,
  Mail,
  FileText,
  Server,
  Link,
  Settings,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>,
  Clock,
} from "lucide-react";
import { TTL_OPTIONS } from "@/constants/dnsRecords";
import { toast } from "react-toastify";

export default function DnsRecordList({ records, onEdit, onDelete, domain }) {
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  // Get record type icon with accessibility
  const getRecordTypeIcon = (type) => {
    const icons = {
      A: { icon: Globe, label: "IPv4 Address" },
      AAAA: { icon: Globe, label: "IPv6 Address" },
      CNAME: { icon: Link, label: "Canonical Name" },
      MX: { icon: Mail, label: "Mail Exchange" },
      TXT: { icon: FileText, label: "Text Record" },
      NS: { icon: Server, label: "Name Server" },
      SRV: { icon: Settings, label: "Service Record" },
    };
    const config = icons[type] || { icon: Globe, label: "DNS Record" };
    const IconComponent = config.icon;
    return (
      <IconComponent
        className="h-4 w-4"
        aria-label={config.label}
        role="img"
      />
    );
  };

  // Get record type styling
  const getRecordTypeStyle = (type) => {
    const styles = {
      A: "bg-blue-100 text-blue-800 border-blue-200",
      AAAA: "bg-blue-100 text-blue-800 border-blue-200",
      CNAME: "bg-purple-100 text-purple-800 border-purple-200",
      MX: "bg-green-100 text-green-800 border-green-200",
      TXT: "bg-orange-100 text-orange-800 border-orange-200",
      NS: "bg-gray-100 text-gray-800 border-gray-200",
      SRV: "bg-pink-100 text-pink-800 border-pink-200",
    };
    return styles[type] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  // Format TTL display
  const formatTTL = (ttl) => {
    const numericTTL = parseInt(ttl);

    // Check if it matches a predefined option
    const option = TTL_OPTIONS.find((opt) => opt.value === numericTTL);
    if (option) {
      return option.label;
    }

    // Format custom TTL values in a human-readable way
    if (numericTTL >= 86400) {
      const days = Math.floor(numericTTL / 86400);
      const remainder = numericTTL % 86400;
      if (remainder === 0) {
        return `${days} day${days !== 1 ? 's' : ''}`;
      }
      return `${numericTTL}s`;
    } else if (numericTTL >= 3600) {
      const hours = Math.floor(numericTTL / 3600);
      const remainder = numericTTL % 3600;
      if (remainder === 0) {
        return `${hours} hour${hours !== 1 ? 's' : ''}`;
      }
      return `${numericTTL}s`;
    } else if (numericTTL >= 60) {
      const minutes = Math.floor(numericTTL / 60);
      const remainder = numericTTL % 60;
      if (remainder === 0) {
        return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
      }
      return `${numericTTL}s`;
    }

    return `${numericTTL}s`;
  };

  // Format record name for display
  const formatRecordName = (name) => {
    if (name === "@") {
      return `@ (${domain?.name})`;
    }
    if (name === "") {
      return `@ (${domain?.name})`;
    }
    return `${name}.${domain?.name}`;
  };

  // Copy content to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success("Copied to clipboard");
    });
  };

  // Handle delete confirmation
  const handleDeleteClick = (record) => {
    setDeleteConfirm(record);
  };

  const handleDeleteConfirm = () => {
    if (deleteConfirm) {
      onDelete(deleteConfirm.id);
      setDeleteConfirm(null);
    }
  };

  // Render record content in a clean, accessible way
  const renderRecordContent = (record) => {
    const { type, content, priority, weight, port } = record;

    // Truncate long content for better display
    const displayContent = content.length > 40 ?
      `${content.substring(0, 40)}...` : content;

    return (
      <div className="flex items-center gap-2 min-w-0">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span
              className="font-mono text-sm text-gray-900 truncate"
              title={content}
            >
              {displayContent}
            </span>
            {/* Additional info for special record types */}
            {type === "MX" && priority && (
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                Priority: {priority}
              </span>
            )}
            {type === "SRV" && (priority || weight || port) && (
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {priority && `P:${priority}`}
                {weight && ` W:${weight}`}
                {port && ` Port:${port}`}
              </span>
            )}
          </div>
        </div>
        <Button
          variant="text"
          size="sm"
          onClick={() => copyToClipboard(content)}
          className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-1 rounded transition-colors flex-shrink-0"
          aria-label={`Copy ${type} record value to clipboard`}
        >
          <Copy className="h-3 w-3" />
        </Button>
      </div>
    );
  };

  if (records.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="mx-auto max-w-sm">
          <Server className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <Typography variant="h6" className="text-gray-600 mb-2">
            No DNS Records Found
          </Typography>
          <Typography className="text-gray-500 text-sm">
            No DNS records match your current filter. Try adjusting your search or add a new record.
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Clean table-like layout */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {/* Table Header */}
        <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
          <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
            <div className="col-span-2">Type</div>
            <div className="col-span-3">Name</div>
            <div className="col-span-4">Value</div>
            <div className="col-span-2">TTL</div>
            <div className="col-span-1 text-right">Actions</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {records.map((record, index) => (
            <div
              key={record.id}
              className="px-6 py-4 hover:bg-gray-50 transition-colors duration-150"
              role="row"
              tabIndex={0}
              aria-label={`DNS record ${index + 1}: ${record.type} record for ${formatRecordName(record.name)}`}
            >
              <div className="grid grid-cols-12 gap-4 items-center">
                {/* Type Column */}
                <div className="col-span-2">
                  <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium border ${getRecordTypeStyle(record.type)}`}>
                    {getRecordTypeIcon(record.type)}
                    <span>{record.type}</span>
                  </div>
                </div>

                {/* Name Column */}
                <div className="col-span-3">
                  <Typography className="font-medium text-gray-900 text-sm">
                    {formatRecordName(record.name)}
                  </Typography>
                </div>

                {/* Value Column */}
                <div className="col-span-4">
                  <div className="flex items-center gap-2">
                    <div className="flex-1 min-w-0">
                      {renderRecordContent(record)}
                    </div>
                  </div>
                </div>

                {/* TTL Column */}
                <div className="col-span-2">
                  <Typography className="text-sm text-gray-600">
                    {formatTTL(record.ttl)}
                  </Typography>
                </div>

                {/* Actions Column */}
                <div className="col-span-1 flex justify-end gap-1">
                  <Button
                    variant="text"
                    size="sm"
                    onClick={() => onEdit(record)}
                    className="text-blue-600 hover:bg-blue-50 p-2 rounded-md transition-colors"
                    aria-label={`Edit ${record.type} record for ${formatRecordName(record.name)}`}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="text"
                    size="sm"
                    onClick={() => handleDeleteClick(record)}
                    className="text-red-600 hover:bg-red-50 p-2 rounded-md transition-colors"
                    aria-label={`Delete ${record.type} record for ${formatRecordName(record.name)}`}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Clean Delete Confirmation Dialog */}
      <Dialog
        open={!!deleteConfirm}
        handler={() => setDeleteConfirm(null)}
        size="sm"
        className="bg-white rounded-lg shadow-xl"
      >
        <DialogHeader className="flex items-center gap-3 pb-4 border-b border-gray-200">
          <div className="flex items-center justify-center w-10 h-10 bg-red-100 rounded-full">
            <AlertTriangle className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <Typography variant="h5" className="text-gray-900">
              Delete DNS Record
            </Typography>
            <Typography className="text-sm text-gray-500 mt-1">
              This action cannot be undone
            </Typography>
          </div>
        </DialogHeader>

        <DialogBody className="py-6">
          {deleteConfirm && (
            <div className="space-y-4">
              <Typography className="text-gray-700">
                Are you sure you want to delete this DNS record?
              </Typography>

              {/* Record Preview */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium border ${getRecordTypeStyle(deleteConfirm.type)}`}>
                    {getRecordTypeIcon(deleteConfirm.type)}
                    <span>{deleteConfirm.type}</span>
                  </div>
                  <Typography className="font-medium text-gray-900">
                    {formatRecordName(deleteConfirm.name)}
                  </Typography>
                </div>
                <Typography className="text-sm text-gray-600 font-mono bg-white px-3 py-2 rounded border">
                  {deleteConfirm.content}
                </Typography>
              </div>

              {/* Warning */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <Typography className="text-sm font-medium text-amber-800 mb-1">
                      Important Notice
                    </Typography>
                    <Typography className="text-sm text-amber-700">
                      Deleting this record may affect your domain's functionality.
                      DNS changes can take up to 24-48 hours to propagate worldwide.
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogBody>

        <DialogFooter className="flex gap-3 pt-4 border-t border-gray-200">
          <Button
            variant="outlined"
            onClick={() => setDeleteConfirm(null)}
            className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            color="red"
            onClick={handleDeleteConfirm}
            className="flex-1 bg-red-600 hover:bg-red-700 flex items-center justify-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Delete Record
          </Button>
        </DialogFooter>
      </Dialog>
    </>
  );
}
