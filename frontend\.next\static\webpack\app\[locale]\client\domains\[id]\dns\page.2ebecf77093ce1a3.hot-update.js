"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [editingRecord, setEditingRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                setDnsServiceActive(true);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load DNS records. DNS service may not be activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].activateDnsService({\n                domainId: domain.id\n            });\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS service activated successfully!\");\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                setShowAddForm(false);\n                setSelectedRecordType(\"A\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateDnsRecord(domain.id, editingRecord.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                setEditingRecord(null);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async (recordId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].deleteDnsRecord(domain.id, recordId);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type);\n    };\n    // Handle add button click\n    const handleAddClick = (recordType)=>{\n        setSelectedRecordType(recordType);\n        setShowAddForm(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            color: \"amber\",\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                \"Domain information is required to manage DNS records.\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h4\",\n                                className: \"text-gray-900\",\n                                children: \"DNS Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Manage DNS records for \",\n                                    domain.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>handleAddClick(activeTab),\n                                disabled: !dnsServiceActive,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Record\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"amber\",\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"font-medium\",\n                                        children: \"DNS Service Not Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"text-sm\",\n                                        children: \"Activate DNS service to manage DNS records for this domain.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"sm\",\n                        onClick: activateDnsService,\n                        disabled: activatingService,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 35\n                            }, this),\n                            \"Activate DNS\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n                        value: activeTab,\n                        onChange: setActiveTab,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsHeader, {\n                                className: \"bg-gray-50 p-1 m-6 mb-0\",\n                                children: recordTypes.map((param)=>{\n                                    let { type, label } = param;\n                                    const count = getRecordsForType(type).length;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tab, {\n                                        value: type,\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 21\n                                            }, this),\n                                            count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsBody, {\n                                className: \"p-6\",\n                                children: recordTypes.map((param)=>{\n                                    let { type } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabPanel, {\n                                        value: type,\n                                        className: \"p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            records: getRecordsForType(type),\n                                            recordType: type,\n                                            onEdit: setEditingRecord,\n                                            onDelete: handleDeleteRecord,\n                                            onAdd: handleAddClick,\n                                            domain: domain,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, type, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAddForm || !!editingRecord,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: editingRecord ? handleEditRecord : handleAddRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"m9UUzP3GoQqildbNfdPac/Odyk4=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});