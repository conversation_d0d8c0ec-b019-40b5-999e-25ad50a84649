// DNS Record Types and Configuration
export const DNS_RECORD_TYPES = {
  A: {
    name: "A",
    label: "A Record",
    description: "Points a domain to an IPv4 address",
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
        placeholder: "@, www, mail, etc.",
      },
      {
        name: "content",
        label: "IPv4 Address",
        type: "text",
        required: true,
        placeholder: "***********",
      },
      {
        name: "ttl",
        label: "TTL (seconds)",
        type: "number",
        required: true,
        default: 14400,
        placeholder: "14400",
        min: 300,
        max: 604800,
      },
    ],
    validation: {
      content:
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    },
  },
  AAAA: {
    name: "AAAA",
    label: "AAAA Record",
    description: "Points a domain to an IPv6 address",
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
        placeholder: "@, www, mail, etc.",
      },
      {
        name: "content",
        label: "IPv6 Address",
        type: "text",
        required: true,
        placeholder: "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
      },
      {
        name: "ttl",
        label: "TTL",
        type: "select",
        required: true,
        default: 14400,
      },
    ],
    validation: {
      content: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/,
    },
  },
  CNAME: {
    name: "CNAME",
    label: "CNAME Record",
    description: "Points a domain to another domain name",
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
        placeholder: "www, blog, shop, etc.",
      },
      {
        name: "content",
        label: "Target Domain",
        type: "text",
        required: true,
        placeholder: "example.com",
      },
      {
        name: "ttl",
        label: "TTL",
        type: "select",
        required: true,
        default: 14400,
      },
    ],
    validation: {
      content:
        /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.?$/,
    },
    restrictions: {
      nameCannotBe: ["@"], // CNAME cannot be used for root domain
      message:
        "CNAME records cannot be used for the root domain (@). Use A or AAAA records instead.",
    },
  },
  MX: {
    name: "MX",
    label: "MX Record",
    description: "Specifies mail servers for the domain",
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
        placeholder: "@, mail, etc.",
      },
      {
        name: "content",
        label: "Mail Server",
        type: "text",
        required: true,
        placeholder: "mail.example.com",
      },
      {
        name: "priority",
        label: "Priority",
        type: "number",
        required: true,
        placeholder: "10",
        min: 0,
        max: 65535,
      },
      {
        name: "ttl",
        label: "TTL",
        type: "select",
        required: true,
        default: 14400,
      },
    ],
    validation: {
      content:
        /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.?$/,
      priority:
        /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,
    },
  },
  TXT: {
    name: "TXT",
    label: "TXT Record",
    description:
      "Stores text information for various purposes (SPF, DKIM, verification, etc.)",
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
        placeholder: "@, _dmarc, _spf, etc.",
      },
      {
        name: "content",
        label: "Text Content",
        type: "textarea",
        required: true,
        placeholder: "v=spf1 include:_spf.google.com ~all",
      },
      {
        name: "ttl",
        label: "TTL",
        type: "select",
        required: true,
        default: 14400,
      },
    ],
    validation: {
      content: /^.{1,255}$/, // TXT records can contain any text up to 255 characters
    },
  },
  NS: {
    name: "NS",
    label: "NS Record",
    description: "Delegates a subdomain to different nameservers",
    fields: [
      {
        name: "name",
        label: "Subdomain",
        type: "text",
        required: true,
        placeholder: "subdomain",
      },
      {
        name: "content",
        label: "Nameserver",
        type: "text",
        required: true,
        placeholder: "ns1.example.com",
      },
      {
        name: "ttl",
        label: "TTL",
        type: "select",
        required: true,
        default: 14400,
      },
    ],
    validation: {
      content:
        /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.?$/,
    },
    restrictions: {
      nameCannotBe: ["@"], // NS records for root domain should be managed at registrar level
      message:
        "NS records for the root domain (@) should be managed through nameserver settings, not DNS records.",
    },
  },
  SRV: {
    name: "SRV",
    label: "SRV Record",
    description: "Specifies the location of services (port and hostname)",
    fields: [
      {
        name: "name",
        label: "Service Name",
        type: "text",
        required: true,
        placeholder: "_service._protocol (e.g., _sip._tcp)",
      },
      {
        name: "content",
        label: "Target Host",
        type: "text",
        required: true,
        placeholder: "target.example.com",
      },
      {
        name: "priority",
        label: "Priority",
        type: "number",
        required: true,
        placeholder: "10",
        min: 0,
        max: 65535,
      },
      {
        name: "weight",
        label: "Weight",
        type: "number",
        required: true,
        placeholder: "5",
        min: 0,
        max: 65535,
      },
      {
        name: "port",
        label: "Port",
        type: "number",
        required: true,
        placeholder: "5060",
        min: 1,
        max: 65535,
      },
      {
        name: "ttl",
        label: "TTL",
        type: "select",
        required: true,
        default: 14400,
      },
    ],
    validation: {
      name: /^_[a-zA-Z0-9-]+\._[a-zA-Z0-9-]+$/,
      content:
        /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.?$/,
      priority:
        /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,
      weight:
        /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,
      port: /^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,
    },
  },
};

// TTL (Time To Live) options in seconds
export const TTL_OPTIONS = [
  { value: 300, label: "5 minutes" },
  { value: 600, label: "10 minutes" },
  { value: 1800, label: "30 minutes" },
  { value: 3600, label: "1 hour" },
  { value: 7200, label: "2 hours" },
  { value: 14400, label: "4 hours" },
  { value: 28800, label: "8 hours" },
  { value: 43200, label: "12 hours" },
  { value: 86400, label: "1 day" },
  { value: 172800, label: "2 days" },
  { value: 604800, label: "1 week" },
];

// Default TTL value
export const DEFAULT_TTL = 14400;

// Common DNS record presets for quick setup
export const DNS_PRESETS = {
  "Google Workspace": {
    description: "Configure Google Workspace email and services",
    records: [
      {
        type: "MX",
        name: "@",
        content: "aspmx.l.google.com",
        priority: 1,
        ttl: 14400,
      },
      {
        type: "MX",
        name: "@",
        content: "alt1.aspmx.l.google.com",
        priority: 5,
        ttl: 14400,
      },
      {
        type: "MX",
        name: "@",
        content: "alt2.aspmx.l.google.com",
        priority: 5,
        ttl: 14400,
      },
      {
        type: "MX",
        name: "@",
        content: "alt3.aspmx.l.google.com",
        priority: 10,
        ttl: 14400,
      },
      {
        type: "MX",
        name: "@",
        content: "alt4.aspmx.l.google.com",
        priority: 10,
        ttl: 14400,
      },
      {
        type: "TXT",
        name: "@",
        content: "v=spf1 include:_spf.google.com ~all",
        ttl: 14400,
      },
    ],
  },
  "Microsoft 365": {
    description: "Configure Microsoft 365 email and services",
    records: [
      {
        type: "MX",
        name: "@",
        content: "{domain}.mail.protection.outlook.com",
        priority: 0,
        ttl: 14400,
      },
      {
        type: "TXT",
        name: "@",
        content: "v=spf1 include:spf.protection.outlook.com -all",
        ttl: 14400,
      },
      {
        type: "CNAME",
        name: "autodiscover",
        content: "autodiscover.outlook.com",
        ttl: 14400,
      },
    ],
  },
  Cloudflare: {
    description: "Basic Cloudflare setup",
    records: [
      { type: "A", name: "@", content: "*********", ttl: 300 },
      { type: "A", name: "www", content: "*********", ttl: 300 },
    ],
  },
};

// Validation helper functions
export const validateDnsRecord = (type, field, value) => {
  const recordType = DNS_RECORD_TYPES[type];
  if (!recordType || !recordType.validation || !recordType.validation[field]) {
    return { isValid: true };
  }

  const regex = recordType.validation[field];
  const isValid = regex.test(value);

  return {
    isValid,
    message: isValid ? null : getValidationMessage(type, field),
  };
};

const getValidationMessage = (type, field) => {
  const messages = {
    A: {
      content: "Please enter a valid IPv4 address (e.g., ***********)",
    },
    AAAA: {
      content:
        "Please enter a valid IPv6 address (e.g., 2001:0db8:85a3::8a2e:0370:7334)",
    },
    CNAME: {
      content: "Please enter a valid domain name (e.g., example.com)",
    },
    MX: {
      content:
        "Please enter a valid mail server domain (e.g., mail.example.com)",
      priority: "Priority must be a number between 0 and 65535",
    },
    TXT: {
      content:
        "Text content cannot be empty and must be less than 255 characters",
    },
    NS: {
      content: "Please enter a valid nameserver domain (e.g., ns1.example.com)",
    },
    SRV: {
      name: "Service name must be in format _service._protocol (e.g., _sip._tcp)",
      content:
        "Please enter a valid target hostname (e.g., target.example.com)",
      priority: "Priority must be a number between 0 and 65535",
      weight: "Weight must be a number between 0 and 65535",
      port: "Port must be a number between 1 and 65535",
    },
  };

  return messages[type]?.[field] || "Invalid value";
};

// Check if a record type has restrictions for certain names
export const checkRecordRestrictions = (type, name) => {
  const recordType = DNS_RECORD_TYPES[type];
  if (!recordType?.restrictions) {
    return { isValid: true };
  }

  const { nameCannotBe, message } = recordType.restrictions;
  const isValid = !nameCannotBe.includes(name);

  return {
    isValid,
    message: isValid ? null : message,
  };
};

// Get all available DNS record types as array
export const getDnsRecordTypes = () => {
  return Object.keys(DNS_RECORD_TYPES).map((key) => ({
    value: key,
    label: DNS_RECORD_TYPES[key].label,
    description: DNS_RECORD_TYPES[key].description,
  }));
};
