"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [editingRecord, setEditingRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                setDnsServiceActive(true);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load DNS records. DNS service may not be activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                setShowAddForm(false);\n                setSelectedRecordType(\"A\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateDnsRecord(domain.id, editingRecord.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                setEditingRecord(null);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async (recordId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].deleteDnsRecord(domain.id, recordId);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Handle add button click\n    const handleAddClick = (recordType)=>{\n        setSelectedRecordType(recordType);\n        setShowAddForm(true);\n    };\n    // Update DNS service status when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDnsServiceActive((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.dnsActivated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            color: \"amber\",\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                \"Domain information is required to manage DNS records.\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h4\",\n                                className: \"text-gray-900\",\n                                children: \"DNS Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Manage DNS records for \",\n                                    domain.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>handleAddClick(activeTab),\n                                disabled: !dnsServiceActive,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Record\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"amber\",\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"font-medium\",\n                                        children: \"DNS Service Not Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"text-sm\",\n                                        children: \"Activate DNS service to manage DNS records for this domain.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"sm\",\n                        onClick: activateDnsService,\n                        disabled: activatingService,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 315,\n                                columnNumber: 35\n                            }, this),\n                            \"Activate DNS\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n                        value: activeTab,\n                        onChange: setActiveTab,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsHeader, {\n                                className: \"bg-gray-50 p-1 m-6 mb-0\",\n                                children: recordTypes.map((param)=>{\n                                    let { type, label } = param;\n                                    const count = getRecordsForType(type).length;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tab, {\n                                        value: type,\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, this),\n                                            count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsBody, {\n                                className: \"p-6\",\n                                children: recordTypes.map((param)=>{\n                                    let { type } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabPanel, {\n                                        value: type,\n                                        className: \"p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            records: getRecordsForType(type),\n                                            recordType: type,\n                                            onEdit: setEditingRecord,\n                                            onDelete: handleDeleteRecord,\n                                            onAdd: handleAddClick,\n                                            domain: domain,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, type, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAddForm || !!editingRecord,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: editingRecord ? handleEditRecord : handleAddRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"x34ckAGxCKgSvE5o0MPLY1z/ePs=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});