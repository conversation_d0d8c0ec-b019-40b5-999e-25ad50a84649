"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/dns/page.jsx":
/*!***********************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/dns/page.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDnsManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_ImprovedDnsManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/ImprovedDnsManager */ \"(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_DnsApiTester__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/domains/DnsApiTester */ \"(app-pages-browser)/./src/components/domains/DnsApiTester.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction DomainDnsManagementPage() {\n    _s();\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"records\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to update domain data\n    const updateDomain = (updatedDomain)=>{\n        setDomain((prevDomain)=>({\n                ...prevDomain,\n                ...updatedDomain\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchDomainData = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Get domain data from user domains\n                const domainsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                if (domainsResponse.data && domainsResponse.data.domains) {\n                    const foundDomain = domainsResponse.data.domains.find((d)=>d.id === id);\n                    if (!foundDomain) {\n                        setError(\"Domain not found\");\n                        return;\n                    }\n                    // Get detailed domain information\n                    try {\n                        const detailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetailsByName(foundDomain.name, \"All\");\n                        if (detailsResponse.data.success && detailsResponse.data.domain) {\n                            const domainDetails = detailsResponse.data.domain;\n                            // Merge domain data with details\n                            const enrichedDomain = {\n                                ...foundDomain,\n                                ...domainDetails,\n                                // Ensure we keep the original ID\n                                id: foundDomain.id\n                            };\n                            setDomain(enrichedDomain);\n                        } else {\n                            // Use basic domain data if details fetch fails\n                            setDomain(foundDomain);\n                        }\n                    } catch (detailsError) {\n                        console.warn(\"Could not fetch domain details:\", detailsError);\n                        // Use basic domain data if details fetch fails\n                        setDomain(foundDomain);\n                    }\n                } else {\n                    setError(\"Failed to load domain data\");\n                }\n            } catch (error) {\n                console.error(\"Error fetching domain data:\", error);\n                setError(\"Failed to load domain information\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load domain information\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (id) {\n            fetchDomainData();\n        }\n    }, [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.orderid) {\n            console.log(\"Order ID available:\", domain.orderid);\n        } else {\n            console.warn(\"Order ID is missing for domain:\", domain);\n        }\n    }, [\n        domain\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[60vh]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                className: \"text-gray-600\",\n                                children: t(\"loading\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8 bg-gray-50 min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"text\",\n                        className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                        onClick: ()=>router.push(\"/client/domains\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            dt(\"back_to_domains\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                        color: \"red\",\n                        className: \"max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            error || \"Domain not found\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, this);\n    }\n    const tabsData = [\n        {\n            label: \"DNS Records\",\n            value: \"records\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Manage A, AAAA, CNAME, MX, TXT, NS, and SRV records\"\n        },\n        {\n            label: \"Nameservers\",\n            value: \"nameservers\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: \"Configure domain nameservers\"\n        },\n        {\n            label: \"API Testing\",\n            value: \"testing\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: \"Test DNS API endpoints\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Breadcrumbs, {\n                                    className: \"bg-white py-2 px-3 rounded-lg border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"text\",\n                                            className: \"text-blue-600 hover:text-blue-800 p-0\",\n                                            onClick: ()=>router.push(\"/client/domains\"),\n                                            children: t(\"domains\", {\n                                                defaultValue: \"Domains\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-gray-800 font-medium\",\n                                                    children: t(\"dns_management\", {\n                                                        defaultValue: \"DNS Management\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full \".concat(domain.status === \"active\" || domain.currentstatus === \"Active\" ? \"bg-green-500\" : domain.status === \"pending\" ? \"bg-yellow-500\" : \"bg-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    variant: \"h4\",\n                                                    className: \"text-gray-900\",\n                                                    children: domain.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this),\n                                                domain.domainOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"ID: \",\n                                                        domain.domainOrderId\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    content: \"Domain Security\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-green-50 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    content: \"DNS Status\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-blue-50 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                            className: \"text-gray-600\",\n                            children: t(\"manage_dns_settings_description\", {\n                                defaultValue: \"Configure DNS records and nameservers for your domain. Changes may take up to 24-48 hours to propagate globally.\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-white border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                                    className: \"bg-gray-50 border-b\",\n                                    indicatorProps: {\n                                        className: \"bg-transparent\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"DNS Records\",\n                                            value: \"records\",\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                            description: \"Set up where your domain points to\"\n                                        },\n                                        {\n                                            label: \"Nameservers\",\n                                            value: \"nameservers\",\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            description: \"Configure your domain's DNS servers\"\n                                        },\n                                        {\n                                            label: \"Health Check\",\n                                            value: \"testing\",\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                            description: \"Check your DNS configuration\"\n                                        }\n                                    ].map((param)=>{\n                                        let { label, value, icon: Icon, description } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                            value: value,\n                                            className: \"flex items-center justify-center gap-2 px-6 py-3 relative text-gray-600 hover:text-gray-900\",\n                                            onClick: ()=>setActiveTab(value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 \".concat(value === activeTab ? \"text-blue-500\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this),\n                                                value === activeTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 w-full h-0.5 bg-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, value, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                                    className: \"relative min-h-[400px]\",\n                                    children: [\n                                        {\n                                            value: \"records\",\n                                            title: \"DNS Records\",\n                                            description: \"DNS records tell the internet where to find your website, email, and other services\",\n                                            guide: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3 p-4 bg-gray-50 rounded-lg mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-white rounded border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2 text-blue-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"A Record\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Connect to web hosting (IPv4)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-white rounded border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2 text-purple-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"CNAME\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Point to another domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-white rounded border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2 text-green-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"MX Record\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Set up email servers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-white rounded border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2 text-orange-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"TXT Record\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Verify domain ownership\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 23\n                                            }, this),\n                                            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_ImprovedDnsManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: updateDomain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 23\n                                            }, this)\n                                        },\n                                        {\n                                            value: \"nameservers\",\n                                            title: \"Nameservers\",\n                                            description: \"Nameservers are the internet's address book for your domain\",\n                                            guide: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 mb-6 bg-blue-50 border border-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-blue-900 mb-1\",\n                                                                    children: \"About Nameservers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"text-sm text-blue-800\",\n                                                                    children: \"Nameservers control who manages your domain's DNS settings. Only change these if you're moving to a different DNS provider.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 23\n                                            }, this),\n                                            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: updateDomain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 23\n                                            }, this)\n                                        },\n                                        {\n                                            value: \"testing\",\n                                            title: \"DNS Health Check\",\n                                            description: \"Verify your DNS configuration is working correctly\",\n                                            guide: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 mb-6 bg-green-50 border border-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-green-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-green-900 mb-1\",\n                                                                    children: \"DNS Health Check\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: \"Regularly check your DNS configuration to ensure everything is working correctly and propagating across the internet.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 23\n                                            }, this),\n                                            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_DnsApiTester__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                domain: domain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 32\n                                            }, this)\n                                        }\n                                    ].map((param)=>{\n                                        let { value, title, description, guide, component } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                            value: value,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        variant: \"h4\",\n                                                                        className: \"text-gray-800\",\n                                                                        children: title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        content: description,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            guide\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    component\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 bg-blue-50 border border-blue-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        className: \"font-medium text-blue-900 mb-2\",\n                                        children: \"Important DNS Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm space-y-2 text-blue-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"DNS changes take 24-48 hours to propagate globally\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Always backup your current DNS settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Use Health Check to verify your setup\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDnsManagementPage, \"PPmgeJW0xHd6YO6+cvmPTeSqUBY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations\n    ];\n});\n_c = DomainDnsManagementPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDnsManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/dns/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx":
/*!**************************************************!*\
  !*** ./src/components/domains/DnsRecordForm.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsRecordForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Info,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Info,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Info,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Info,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DnsRecordForm(param) {\n    let { isOpen, onClose, onSubmit, initialData = null, domain, selectedType = \"A\", onTypeChange } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: selectedType,\n        name: \"\",\n        content: \"\",\n        ttl: _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_TTL,\n        priority: \"\",\n        weight: \"\",\n        port: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!initialData;\n    const recordTypes = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.getDnsRecordTypes)();\n    // Initialize form data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            setFormData({\n                type: initialData.type,\n                name: initialData.name || \"\",\n                content: initialData.content || \"\",\n                ttl: initialData.ttl || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_TTL,\n                priority: initialData.priority || \"\",\n                weight: initialData.weight || \"\",\n                port: initialData.port || \"\"\n            });\n        } else {\n            setFormData({\n                type: selectedType,\n                name: \"\",\n                content: \"\",\n                ttl: _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_TTL,\n                priority: \"\",\n                weight: \"\",\n                port: \"\"\n            });\n        }\n        setErrors({});\n    }, [\n        initialData,\n        selectedType,\n        isOpen\n    ]);\n    // Get current record type configuration\n    const currentRecordType = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.DNS_RECORD_TYPES[formData.type];\n    // Handle form field changes\n    const handleFieldChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error for this field\n        if (errors[field]) {\n            setErrors((prev)=>{\n                const newErrors = {\n                    ...prev\n                };\n                delete newErrors[field];\n                return newErrors;\n            });\n        }\n        // Update parent component's selected type if type changes\n        if (field === \"type\" && onTypeChange) {\n            onTypeChange(value);\n        }\n    };\n    // Validate form\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!currentRecordType) {\n            newErrors.type = \"Invalid record type\";\n            setErrors(newErrors);\n            return false;\n        }\n        // Validate each required field\n        currentRecordType.fields.forEach((field)=>{\n            const value = formData[field.name];\n            // Check if required field is empty\n            if (field.required && (!value || value.toString().trim() === \"\")) {\n                newErrors[field.name] = \"\".concat(field.label, \" is required\");\n                return;\n            }\n            // Skip validation for empty optional fields\n            if (!value || value.toString().trim() === \"\") {\n                return;\n            }\n            // Validate field format\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.validateDnsRecord)(formData.type, field.name, value);\n            if (!validation.isValid) {\n                newErrors[field.name] = validation.message;\n            }\n            // Check number field ranges\n            if (field.type === \"number\") {\n                const numValue = parseInt(value);\n                if (field.min !== undefined && numValue < field.min) {\n                    newErrors[field.name] = \"Minimum value is \".concat(field.min);\n                }\n                if (field.max !== undefined && numValue > field.max) {\n                    newErrors[field.name] = \"Maximum value is \".concat(field.max);\n                }\n            }\n        });\n        // Check record type restrictions\n        const restrictionCheck = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.checkRecordRestrictions)(formData.type, formData.name);\n        if (!restrictionCheck.isValid) {\n            newErrors.name = restrictionCheck.message;\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        try {\n            setIsSubmitting(true);\n            // Prepare submission data\n            const submissionData = {\n                type: formData.type,\n                name: formData.name.trim(),\n                content: formData.content.trim(),\n                ttl: parseInt(formData.ttl)\n            };\n            // Add optional fields based on record type\n            if (currentRecordType.fields.some((f)=>f.name === \"priority\")) {\n                submissionData.priority = formData.priority ? parseInt(formData.priority) : null;\n            }\n            if (currentRecordType.fields.some((f)=>f.name === \"weight\")) {\n                submissionData.weight = formData.weight ? parseInt(formData.weight) : null;\n            }\n            if (currentRecordType.fields.some((f)=>f.name === \"port\")) {\n                submissionData.port = formData.port ? parseInt(formData.port) : null;\n            }\n            await onSubmit(submissionData);\n        } catch (error) {\n            console.error(\"Error submitting DNS record:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Render form field based on type\n    const renderField = (field)=>{\n        const value = formData[field.name];\n        const error = errors[field.name];\n        switch(field.type){\n            case \"select\":\n                if (field.name === \"ttl\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                        label: field.label,\n                        value: value.toString(),\n                        onChange: (val)=>handleFieldChange(field.name, parseInt(val)),\n                        error: !!error,\n                        children: _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.TTL_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                value: option.value.toString(),\n                                children: option.label\n                            }, option.value, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                lineNumber: 209,\n                                columnNumber: 17\n                            }, this))\n                    }, field.name, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                        lineNumber: 201,\n                        columnNumber: 13\n                    }, this);\n                }\n                break;\n            case \"textarea\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Textarea, {\n                    label: field.label,\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    error: !!error,\n                    rows: 3\n                }, field.name, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this);\n            case \"number\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                    type: \"number\",\n                    label: field.label,\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    min: field.min,\n                    max: field.max,\n                    error: !!error\n                }, field.name, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                    label: field.label,\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    error: !!error\n                }, field.name, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        handler: onClose,\n        size: \"lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                            variant: \"h4\",\n                            children: isEditing ? \"Edit DNS Record\" : \"Add DNS Record\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"text\",\n                            onClick: onClose,\n                            className: \"p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.DialogBody, {\n                    className: \"space-y-4 max-h-96 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                    label: \"Record Type\",\n                                    value: formData.type,\n                                    onChange: (val)=>handleFieldChange(\"type\", val),\n                                    disabled: isEditing,\n                                    children: recordTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                            value: type.value,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        className: \"font-medium\",\n                                                        children: type.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: type.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, type.value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"text-red-500 text-xs mt-1\",\n                                    children: errors.type\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        currentRecordType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                            color: \"blue\",\n                            className: \"py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"text-sm\",\n                                    children: currentRecordType.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this),\n                        currentRecordType === null || currentRecordType === void 0 ? void 0 : currentRecordType.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    renderField(field),\n                                    errors[field.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"text-red-500 text-xs mt-1\",\n                                        children: errors[field.name]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, field.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                            color: \"amber\",\n                            className: \"py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Domain Context:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-xs\",\n                                            children: [\n                                                '• Use \"@\" for the root domain (',\n                                                domain === null || domain === void 0 ? void 0 : domain.name,\n                                                \")\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                '• Use \"www\" for www.',\n                                                domain === null || domain === void 0 ? void 0 : domain.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this),\n                                                '• Use subdomain names like \"mail\" for mail.',\n                                                domain === null || domain === void 0 ? void 0 : domain.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                    className: \"flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            onClick: onClose,\n                            disabled: isSubmitting,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                            disabled: isSubmitting,\n                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, this),\n                                    isEditing ? \"Updating...\" : \"Adding...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Info_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    isEditing ? \"Update Record\" : \"Add Record\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n            lineNumber: 262,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordForm.jsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(DnsRecordForm, \"BSUnbMy/ztZPS8aWKYfGquI1XwY=\");\n_c = DnsRecordForm;\nvar _c;\n$RefreshReg$(_c, \"DnsRecordForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx":
/*!***************************************************!*\
  !*** ./src/components/domains/DnsRecordTable.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsRecordTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Edit,FileText,Globe,Link,Mail,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DnsRecordTable(param) {\n    let { records, recordType, onEdit, onDelete, onAdd, domain, loading = false } = param;\n    _s();\n    const [deleteConfirm, setDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get record type configuration\n    const getRecordTypeConfig = (type)=>{\n        const configs = {\n            A: {\n                icon: _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: \"IPv4 Address\",\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-600\",\n                borderColor: \"border-blue-200\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: \"IPv6 Address\",\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-600\",\n                borderColor: \"border-purple-200\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                label: \"Canonical Name\",\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-600\",\n                borderColor: \"border-green-200\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                label: \"Mail Exchange\",\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-600\",\n                borderColor: \"border-orange-200\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                label: \"Text Record\",\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-600\",\n                borderColor: \"border-gray-200\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: \"Name Server\",\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-600\",\n                borderColor: \"border-indigo-200\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: \"Service Record\",\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-600\",\n                borderColor: \"border-pink-200\"\n            }\n        };\n        return configs[type] || configs.A;\n    };\n    // Format TTL display\n    const formatTTL = (ttl)=>{\n        const numericTTL = parseInt(ttl);\n        const option = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_3__.TTL_OPTIONS.find((opt)=>opt.value === numericTTL);\n        if (option) {\n            return option.label;\n        }\n        // Format custom TTL values\n        if (numericTTL >= 86400) {\n            const days = Math.floor(numericTTL / 86400);\n            const remainder = numericTTL % 86400;\n            if (remainder === 0) {\n                return \"\".concat(days, \" day\").concat(days !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 3600) {\n            const hours = Math.floor(numericTTL / 3600);\n            const remainder = numericTTL % 3600;\n            if (remainder === 0) {\n                return \"\".concat(hours, \" hour\").concat(hours !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 60) {\n            const minutes = Math.floor(numericTTL / 60);\n            const remainder = numericTTL % 60;\n            if (remainder === 0) {\n                return \"\".concat(minutes, \" minute\").concat(minutes !== 1 ? \"s\" : \"\");\n            }\n        }\n        return \"\".concat(numericTTL, \"s\");\n    };\n    // Format record name\n    const formatRecordName = (name)=>{\n        if (!name || name === \"@\") return (domain === null || domain === void 0 ? void 0 : domain.name) || \"@\";\n        if (name.endsWith(\".\")) return name.slice(0, -1);\n        return name;\n    };\n    // Copy to clipboard\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (err) {\n            console.error(\"Failed to copy:\", err);\n        }\n    };\n    // Handle delete confirmation\n    const handleDeleteClick = (record)=>{\n        setDeleteConfirm(record);\n    };\n    const handleDeleteConfirm = ()=>{\n        if (deleteConfirm && onDelete) {\n            onDelete(deleteConfirm.id);\n        }\n        setDeleteConfirm(null);\n    };\n    const config = getRecordTypeConfig(recordType);\n    const IconComponent = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(config.bgColor, \" \").concat(config.borderColor, \" border-b px-6 py-4\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(config.bgColor, \" \").concat(config.borderColor, \" border\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"h-5 w-5 \".concat(config.textColor)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        variant: \"h6\",\n                                                        className: \"text-gray-900\",\n                                                        children: [\n                                                            recordType,\n                                                            \" Records\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        variant: \"small\",\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            config.label,\n                                                            \" • \",\n                                                            records.length,\n                                                            \" record\",\n                                                            records.length !== 1 ? \"s\" : \"\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        className: \"flex items-center gap-2 \".concat(config.textColor, \" bg-white border \").concat(config.borderColor, \" hover:\").concat(config.bgColor),\n                                        variant: \"outlined\",\n                                        onClick: ()=>onAdd && onAdd(recordType),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add \",\n                                            recordType\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-12 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-gray-500\",\n                                children: [\n                                    \"Loading \",\n                                    recordType,\n                                    \" records...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this) : records.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-12 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full \".concat(config.bgColor),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"h-6 w-6 \".concat(config.textColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-gray-900 mb-1\",\n                                                children: [\n                                                    \"No \",\n                                                    recordType,\n                                                    \" records found\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: [\n                                                    \"Create your first \",\n                                                    recordType,\n                                                    \" record to get started\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        className: \"flex items-center gap-2 \".concat(config.textColor),\n                                        variant: \"outlined\",\n                                        onClick: ()=>onAdd && onAdd(recordType),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add \",\n                                            recordType,\n                                            \" Record\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 px-6 py-3 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-12 gap-4 text-sm font-medium text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-5\",\n                                                children: \"Value\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2\",\n                                                children: \"TTL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-right\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y divide-gray-200\",\n                                    children: records.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-4 hover:bg-gray-50 transition-colors duration-150\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-12 gap-4 items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                            className: \"font-medium text-gray-900 text-sm\",\n                                                            children: formatRecordName(record.name)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                        className: \"font-mono text-sm text-gray-700 flex-1 min-w-0 truncate\",\n                                                                        children: record.content\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                        content: \"Copy to clipboard\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                            variant: \"text\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>copyToClipboard(record.content),\n                                                                            className: \"p-1 text-gray-400 hover:text-gray-600\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                lineNumber: 270,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            record.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Priority: \",\n                                                                    record.priority\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                            value: formatTTL(record.ttl),\n                                                            size: \"sm\",\n                                                            className: \"bg-gray-100 text-gray-700 text-xs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2 flex justify-end gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>onEdit && onEdit(record),\n                                                                    className: \"text-gray-400 hover:text-blue-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleDeleteClick(record),\n                                                                    className: \"text-gray-400 hover:text-red-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, record.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n                open: !!deleteConfirm,\n                handler: ()=>setDeleteConfirm(null),\n                size: \"sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Edit_FileText_Globe_Link_Mail_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-5 w-5 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            \"Confirm Delete\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.DialogBody, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                children: [\n                                    \"Are you sure you want to delete this \",\n                                    deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.type,\n                                    \" record?\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-gray-50 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: [\n                                        formatRecordName(deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.name),\n                                        \" → \",\n                                        deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.content\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                        className: \"gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                onClick: ()=>setDeleteConfirm(null),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                color: \"red\",\n                                onClick: handleDeleteConfirm,\n                                children: \"Delete Record\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DnsRecordTable, \"hYyWuicfaxWkwjFOLzgigv+gtfI=\");\n_c = DnsRecordTable;\nvar _c;\n$RefreshReg$(_c, \"DnsRecordTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [editingRecord, setEditingRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A Records\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA Records\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME Records\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX Records\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT Records\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS Records\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV Records\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                setDnsServiceActive(true);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load DNS records. DNS service may not be activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].activateDnsService({\n                domainId: domain.id\n            });\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS service activated successfully!\");\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                setShowAddForm(false);\n                setSelectedRecordType(\"A\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateDnsRecord(domain.id, editingRecord.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                setEditingRecord(null);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async (recordId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].deleteDnsRecord(domain.id, recordId);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type);\n    };\n    // Handle add button click\n    const handleAddClick = (recordType)=>{\n        setSelectedRecordType(recordType);\n        setShowAddForm(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            color: \"amber\",\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                \"Domain information is required to manage DNS records.\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h4\",\n                                className: \"text-gray-900\",\n                                children: \"DNS Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Manage DNS records for \",\n                                    domain.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>handleAddClick(activeTab),\n                                disabled: !dnsServiceActive,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Record\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"amber\",\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"font-medium\",\n                                        children: \"DNS Service Not Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"text-sm\",\n                                        children: \"Activate DNS service to manage DNS records for this domain.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"sm\",\n                        onClick: activateDnsService,\n                        disabled: activatingService,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 265,\n                                columnNumber: 35\n                            }, this),\n                            \"Activate DNS\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n                        value: activeTab,\n                        onChange: setActiveTab,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsHeader, {\n                                className: \"bg-gray-50 p-1 m-6 mb-0\",\n                                children: recordTypes.map((param)=>{\n                                    let { type, label } = param;\n                                    const count = getRecordsForType(type).length;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tab, {\n                                        value: type,\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this),\n                                            count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsBody, {\n                                className: \"p-6\",\n                                children: recordTypes.map((param)=>{\n                                    let { type } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabPanel, {\n                                        value: type,\n                                        className: \"p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            records: getRecordsForType(type),\n                                            recordType: type,\n                                            onEdit: setEditingRecord,\n                                            onDelete: handleDeleteRecord,\n                                            onAdd: handleAddClick,\n                                            domain: domain,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, type, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAddForm || !!editingRecord,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: editingRecord ? handleEditRecord : handleAddRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"m9UUzP3GoQqildbNfdPac/Odyk4=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/link.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Link; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\",\n            key: \"1cjeqo\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\",\n            key: \"19qd67\"\n        }\n    ]\n];\nconst Link = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Link\", __iconNode);\n //# sourceMappingURL=link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbGluay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQStEQyxLQUFLO1FBQVM7S0FBRTtJQUM3RjtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFnRUMsS0FBSztRQUFTO0tBQUU7Q0FDL0Y7QUFDRCxNQUFNQyxPQUFPSixnRUFBZ0JBLENBQUMsUUFBUUM7QUFFQyxDQUN2QyxnQ0FBZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9saW5rLmpzP2Y3M2UiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMTAgMTNhNSA1IDAgMCAwIDcuNTQuNTRsMy0zYTUgNSAwIDAgMC03LjA3LTcuMDdsLTEuNzIgMS43MVwiLCBrZXk6IFwiMWNqZXFvXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xNCAxMWE1IDUgMCAwIDAtNy41NC0uNTRsLTMgM2E1IDUgMCAwIDAgNy4wNyA3LjA3bDEuNzEtMS43MVwiLCBrZXk6IFwiMTlxZDY3XCIgfV1cbl07XG5jb25zdCBMaW5rID0gY3JlYXRlTHVjaWRlSWNvbihcIkxpbmtcIiwgX19pY29uTm9kZSk7XG5cbmV4cG9ydCB7IF9faWNvbk5vZGUsIExpbmsgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bGluay5qcy5tYXBcbiJdLCJuYW1lcyI6WyJjcmVhdGVMdWNpZGVJY29uIiwiX19pY29uTm9kZSIsImQiLCJrZXkiLCJMaW5rIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ RefreshCw; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n];\nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"RefreshCw\", __iconNode);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcmVmcmVzaC1jdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQXNEQyxLQUFLO1FBQVM7S0FBRTtJQUNwRjtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFjQyxLQUFLO1FBQVM7S0FBRTtJQUM1QztRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUF1REMsS0FBSztRQUFTO0tBQUU7SUFDckY7UUFBQztRQUFRO1lBQUVELEdBQUc7WUFBYUMsS0FBSztRQUFTO0tBQUU7Q0FDNUM7QUFDRCxNQUFNQyxZQUFZSixnRUFBZ0JBLENBQUMsYUFBYUM7QUFFSixDQUM1QyxzQ0FBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9yZWZyZXNoLWN3LmpzPzIwZWEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOFwiLCBrZXk6IFwidjloNXZjXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0yMSAzdjVoLTVcIiwga2V5OiBcIjFxN3RvMFwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMjEgMTJhOSA5IDAgMCAxLTkgOSA5Ljc1IDkuNzUgMCAwIDEtNi43NC0yLjc0TDMgMTZcIiwga2V5OiBcIjN1aWZsM1wiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNOCAxNkgzdjVcIiwga2V5OiBcIjFjdjY3OFwiIH1dXG5dO1xuY29uc3QgUmVmcmVzaEN3ID0gY3JlYXRlTHVjaWRlSWNvbihcIlJlZnJlc2hDd1wiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgUmVmcmVzaEN3IGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZnJlc2gtY3cuanMubWFwXG4iXSwibmFtZXMiOlsiY3JlYXRlTHVjaWRlSWNvbiIsIl9faWNvbk5vZGUiLCJkIiwia2V5IiwiUmVmcmVzaEN3IiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/settings.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Settings; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\",\n            key: \"1qme2f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n];\nconst Settings = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Settings\", __iconNode);\n //# sourceMappingURL=settings.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/triangle-alert.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ TriangleAlert; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n            key: \"wmoenq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n];\nconst TriangleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TriangleAlert\", __iconNode);\n //# sourceMappingURL=triangle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHJpYW5nbGUtYWxlcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFcUQ7QUFFdEQsTUFBTUMsYUFBYTtJQUNqQjtRQUNFO1FBQ0E7WUFDRUMsR0FBRztZQUNIQyxLQUFLO1FBQ1A7S0FDRDtJQUNEO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQVdDLEtBQUs7UUFBUztLQUFFO0lBQ3pDO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQWNDLEtBQUs7UUFBUztLQUFFO0NBQzdDO0FBQ0QsTUFBTUMsZ0JBQWdCSixnRUFBZ0JBLENBQUMsaUJBQWlCQztBQUVSLENBQ2hELDBDQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3RyaWFuZ2xlLWFsZXJ0LmpzP2VmN2QiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTNcIixcbiAgICAgIGtleTogXCJ3bW9lbnFcIlxuICAgIH1cbiAgXSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTEyIDl2NFwiLCBrZXk6IFwianV6cHU3XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xMiAxN2guMDFcIiwga2V5OiBcInAzMnAwNVwiIH1dXG5dO1xuY29uc3QgVHJpYW5nbGVBbGVydCA9IGNyZWF0ZUx1Y2lkZUljb24oXCJUcmlhbmdsZUFsZXJ0XCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBUcmlhbmdsZUFsZXJ0IGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyaWFuZ2xlLWFsZXJ0LmpzLm1hcFxuIl0sIm5hbWVzIjpbImNyZWF0ZUx1Y2lkZUljb24iLCJfX2ljb25Ob2RlIiwiZCIsImtleSIsIlRyaWFuZ2xlQWxlcnQiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ X; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n];\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"X\", __iconNode);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQWNDLEtBQUs7UUFBUztLQUFFO0lBQzVDO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQWNDLEtBQUs7UUFBUztLQUFFO0NBQzdDO0FBQ0QsTUFBTUMsSUFBSUosZ0VBQWdCQSxDQUFDLEtBQUtDO0FBRUksQ0FDcEMsNkJBQTZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcz9kZjg4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjQ3NS4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgX19pY29uTm9kZSA9IFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE4IDYgNiAxOFwiLCBrZXk6IFwiMWJsNWY4XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIm02IDYgMTIgMTJcIiwga2V5OiBcImQ4Yms2dlwiIH1dXG5dO1xuY29uc3QgWCA9IGNyZWF0ZUx1Y2lkZUljb24oXCJYXCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBYIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXguanMubWFwXG4iXSwibmFtZXMiOlsiY3JlYXRlTHVjaWRlSWNvbiIsIl9faWNvbk5vZGUiLCJkIiwia2V5IiwiWCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ })

});