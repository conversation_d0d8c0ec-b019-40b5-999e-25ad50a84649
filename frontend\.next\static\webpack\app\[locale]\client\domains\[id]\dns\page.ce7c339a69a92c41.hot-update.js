"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx":
/*!*****************************************************!*\
  !*** ./src/components/domains/ModernDnsManager.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModernDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ModernDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dnsRecords, setDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // Format TTL display\n    const formatTTL = (ttl)=>{\n        const numericTTL = parseInt(ttl);\n        // Check if it matches a predefined option\n        const option = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_5__.TTL_OPTIONS.find((opt)=>opt.value === numericTTL);\n        if (option) {\n            return option.label;\n        }\n        // Format custom TTL values in a human-readable way\n        if (numericTTL >= 86400) {\n            const days = Math.floor(numericTTL / 86400);\n            const remainder = numericTTL % 86400;\n            if (remainder === 0) {\n                return \"\".concat(days, \" day\").concat(days !== 1 ? \"s\" : \"\");\n            }\n            return \"\".concat(numericTTL, \"s\");\n        } else if (numericTTL >= 3600) {\n            const hours = Math.floor(numericTTL / 3600);\n            const remainder = numericTTL % 3600;\n            if (remainder === 0) {\n                return \"\".concat(hours, \" hour\").concat(hours !== 1 ? \"s\" : \"\");\n            }\n            return \"\".concat(numericTTL, \"s\");\n        } else if (numericTTL >= 60) {\n            const minutes = Math.floor(numericTTL / 60);\n            const remainder = numericTTL % 60;\n            if (remainder === 0) {\n                return \"\".concat(minutes, \" minute\").concat(minutes !== 1 ? \"s\" : \"\");\n            }\n            return \"\".concat(numericTTL, \"s\");\n        }\n        return \"\".concat(numericTTL, \"s\");\n    };\n    // Form states for adding records\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"@\",\n        content: \"\",\n        ttl: \"14400\"\n    });\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id, {\n                type: selectedRecordType\n            });\n            if (response.data.success) {\n                setDnsRecords(response.data.records || []);\n                setDnsServiceActive(true); // If we can get records, service is active\n            }\n        } catch (error) {\n            console.error(\"Error loading DNS records:\", error);\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    // Update DNS service active state when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain) {\n            console.log(\"\\uD83D\\uDD04 [DNS] Domain data updated:\", domain);\n            // Check if DNS service is already active\n            const isActive = domain.dnsServiceActive || domain.dnsActive || false;\n            console.log(\"\\uD83D\\uDD04 [DNS] Setting DNS service active state:\", isActive);\n            setDnsServiceActive(isActive);\n        }\n    }, [\n        domain\n    ]);\n    // Activate DNS Service\n    const activateDnsService = async ()=>{\n        console.log(\"Activating DNS service for domain:\", domain);\n        // Try to get the order ID from various possible fields\n        const orderIdToUse = (domain === null || domain === void 0 ? void 0 : domain.domainOrderId) || (domain === null || domain === void 0 ? void 0 : domain.orderid) || (domain === null || domain === void 0 ? void 0 : domain.orderId) || (domain === null || domain === void 0 ? void 0 : domain.id);\n        if (!orderIdToUse) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Domain order ID not found. Cannot activate DNS service.\");\n            console.error(\"Domain object:\", domain);\n            return;\n        }\n        console.log(\"Using order ID for DNS activation:\", orderIdToUse);\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDD04 [DNS] Calling DNS activation API with order ID:\", orderIdToUse);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderIdToUse);\n            console.log(\"✅ [DNS] DNS activation response received:\", response.data);\n            console.log(\"✅ [DNS] Response success flag:\", response.data.success);\n            console.log(\"✅ [DNS] Response activated flag:\", response.data.activated);\n            console.log(\"✅ [DNS] Raw API response:\", response.data.rawResponse);\n            if (response.data.success && response.data.activated) {\n                console.log(\"✅ [DNS] DNS service activated successfully, updating UI state\");\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.message || \"DNS service activated successfully!\");\n                // Reload DNS records after activation\n                console.log(\"\\uD83D\\uDD04 [DNS] Reloading DNS records after activation\");\n                await loadDnsRecords();\n                // Force a re-render by updating the domain state\n                if (typeof onUpdate === \"function\") {\n                    console.log(\"\\uD83D\\uDD04 [DNS] Updating domain state via onUpdate callback\");\n                    onUpdate({\n                        dnsServiceActive: true\n                    });\n                }\n            } else {\n                console.error(\"❌ [DNS] DNS activation failed:\", response.data);\n                throw new Error(response.data.error || response.data.message || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response_data1, _error_response2;\n            console.error(\"❌ [DNS] Error activating DNS service:\", error);\n            console.error(\"❌ [DNS] Error response:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            const errorMessage = ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data1 = _error_response2.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message || \"Failed to activate DNS service\";\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service: \" + errorMessage);\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS Record\n    const addDnsRecord = async ()=>{\n        if (!formData.content.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter the record content\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const recordData = {\n                type: selectedRecordType,\n                name: formData.name,\n                content: formData.content,\n                ttl: parseInt(formData.ttl)\n            };\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(selectedRecordType, \" record added successfully!\"));\n                setFormData({\n                    name: \"@\",\n                    content: \"\",\n                    ttl: \"14400\"\n                });\n                setShowAddForm(false);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record: \" + (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.details) || error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Copy to clipboard\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Copied to clipboard!\");\n    };\n    // Get record type icon and color\n    const getRecordTypeInfo = (type)=>{\n        const info = {\n            A: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-700\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-700\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-700\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-700\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-700\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-700\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-700\"\n            }\n        };\n        return info[type] || info.A;\n    };\n    // Format record name for display\n    const formatRecordName = (name)=>{\n        if (name === \"@\" || name === \"\") {\n            return \"@ (\".concat(domain === null || domain === void 0 ? void 0 : domain.name, \")\");\n        }\n        return \"\".concat(name, \".\").concat(domain === null || domain === void 0 ? void 0 : domain.name);\n    };\n    // Available record types (only show implemented ones)\n    const availableRecordTypes = [\n        {\n            value: \"A\",\n            label: \"A Record\",\n            description: \"IPv4 Address\",\n            implemented: true\n        },\n        {\n            value: \"AAAA\",\n            label: \"AAAA Record\",\n            description: \"IPv6 Address\",\n            implemented: true\n        },\n        {\n            value: \"CNAME\",\n            label: \"CNAME Record\",\n            description: \"Domain Alias\",\n            implemented: false\n        },\n        {\n            value: \"MX\",\n            label: \"MX Record\",\n            description: \"Mail Server\",\n            implemented: false\n        },\n        {\n            value: \"TXT\",\n            label: \"TXT Record\",\n            description: \"Text Data\",\n            implemented: false\n        },\n        {\n            value: \"NS\",\n            label: \"NS Record\",\n            description: \"Name Server\",\n            implemented: false\n        },\n        {\n            value: \"SRV\",\n            label: \"SRV Record\",\n            description: \"Service Location\",\n            implemented: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 border-l-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full \".concat(dnsServiceActive ? \"bg-green-100\" : \"bg-gray-100\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 \".concat(dnsServiceActive ? \"text-green-600\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Service Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                        value: dnsServiceActive ? \"Active\" : \"Inactive\",\n                                                        color: dnsServiceActive ? \"green\" : \"gray\",\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: dnsServiceActive ? \"DNS service is active and ready to manage records\" : \"DNS service needs to be activated before managing records\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                onClick: activateDnsService,\n                                disabled: activatingService,\n                                children: [\n                                    activatingService ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Activate DNS Service\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 373,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Manage your domain's DNS records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                        onClick: ()=>setShowAddForm(!showAddForm),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add Record\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 396,\n                                columnNumber: 15\n                            }, this),\n                            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-6 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"text-gray-800 mb-4\",\n                                        children: \"Add New DNS Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"Record Type\",\n                                                value: selectedRecordType,\n                                                onChange: (val)=>setSelectedRecordType(val),\n                                                children: availableRecordTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: type.value,\n                                                        disabled: !type.implemented,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: type.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                !type.implemented && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                    value: \"Soon\",\n                                                                    size: \"sm\",\n                                                                    color: \"amber\",\n                                                                    className: \"text-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: \"Name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                placeholder: \"@, www, mail, etc.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: selectedRecordType === \"A\" ? \"IPv4 Address\" : \"IPv6 Address\",\n                                                value: formData.content,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            content: e.target.value\n                                                        })),\n                                                placeholder: selectedRecordType === \"A\" ? \"***********\" : \"2001:db8::1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 21\n                                            }, this),\n                                            selectedRecordType === \"A\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: \"TTL (seconds)\",\n                                                type: \"number\",\n                                                value: formData.ttl,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            ttl: e.target.value\n                                                        })),\n                                                placeholder: \"14400\",\n                                                min: \"300\",\n                                                max: \"604800\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"TTL\",\n                                                value: formData.ttl,\n                                                onChange: (val)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            ttl: val\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"300\",\n                                                        children: \"5 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"1800\",\n                                                        children: \"30 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"3600\",\n                                                        children: \"1 hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"14400\",\n                                                        children: \"4 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"86400\",\n                                                        children: \"1 day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-700 flex items-center gap-2\",\n                                                onClick: addDnsRecord,\n                                                disabled: loading,\n                                                children: [\n                                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Add Record\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outlined\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 416,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: loading && dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: \"Loading DNS records...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h6\",\n                                            className: \"text-gray-600 mb-2\",\n                                            children: \"No DNS Records Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: \"Start by adding your first DNS record to configure your domain.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            onClick: ()=>setShowAddForm(true),\n                                            children: \"Add Your First Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.map((record)=>{\n                                    const typeInfo = getRecordTypeInfo(record.type);\n                                    const IconComponent = typeInfo.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg \".concat(typeInfo.bgColor),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-5 w-5 \".concat(typeInfo.textColor)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                                value: record.type,\n                                                                                color: typeInfo.color,\n                                                                                className: \"text-xs\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 581,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: formatRecordName(record.name)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 586,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"TTL: \",\n                                                                                    formatTTL(record.ttl)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 589,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-mono text-sm text-gray-700\",\n                                                                                children: record.content\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                                content: \"Copy to clipboard\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                                    variant: \"text\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>copyToClipboard(record.content),\n                                                                                    className: \"p-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                        lineNumber: 607,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                    lineNumber: 599,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 598,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-blue-600 hover:bg-blue-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-red-600 hover:bg-red-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, record.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 533,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 395,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 394,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"blue\",\n                className: \"py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"font-medium mb-1\",\n                                children: \"DNS Management Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 652,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Currently supporting A and AAAA record types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Additional record types (CNAME, MX, TXT, NS, SRV) coming soon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• DNS changes may take 24-48 hours to propagate globally\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Always backup your DNS configuration before making changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 651,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 649,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernDnsManager, \"t1tnPQruhzp95p331Krk30rHPN0=\");\n_c = ModernDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ModernDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx\n"));

/***/ })

});