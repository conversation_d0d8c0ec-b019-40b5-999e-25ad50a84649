"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Plus,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [editingRecord, setEditingRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                setDnsServiceActive(true);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load DNS records. DNS service may not be activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS service activated successfully!\");\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                setShowAddForm(false);\n                setSelectedRecordType(\"A\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateDnsRecord(domain.id, editingRecord.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                setEditingRecord(null);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async (recordId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].deleteDnsRecord(domain.id, recordId);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type);\n    };\n    // Handle add button click\n    const handleAddClick = (recordType)=>{\n        setSelectedRecordType(recordType);\n        setShowAddForm(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            color: \"amber\",\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this),\n                \"Domain information is required to manage DNS records.\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h4\",\n                                className: \"text-gray-900\",\n                                children: \"DNS Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Manage DNS records for \",\n                                    domain.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outlined\",\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>handleAddClick(activeTab),\n                                disabled: !dnsServiceActive,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Record\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"amber\",\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Plus_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"font-medium\",\n                                        children: \"DNS Service Not Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        className: \"text-sm\",\n                                        children: \"Activate DNS service to manage DNS records for this domain.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"sm\",\n                        onClick: activateDnsService,\n                        disabled: activatingService,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 35\n                            }, this),\n                            \"Activate DNS\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n                        value: activeTab,\n                        onChange: setActiveTab,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsHeader, {\n                                className: \"bg-gray-50 p-1 m-6 mb-0\",\n                                children: recordTypes.map((param)=>{\n                                    let { type, label } = param;\n                                    const count = getRecordsForType(type).length;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tab, {\n                                        value: type,\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 21\n                                            }, this),\n                                            count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabsBody, {\n                                className: \"p-6\",\n                                children: recordTypes.map((param)=>{\n                                    let { type } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.TabPanel, {\n                                        value: type,\n                                        className: \"p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            records: getRecordsForType(type),\n                                            recordType: type,\n                                            onEdit: setEditingRecord,\n                                            onDelete: handleDeleteRecord,\n                                            onAdd: handleAddClick,\n                                            domain: domain,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, type, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAddForm || !!editingRecord,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: editingRecord ? handleEditRecord : handleAddRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"m9UUzP3GoQqildbNfdPac/Odyk4=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});